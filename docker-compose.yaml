version: '3'
services:
  mysql:
    build: ./docker/mysql
    container_name: bs-point_mysql
    platform: linux/x86_64
    tty: true
    volumes:
      - ./docker/mysql/conf.d/my.cnf:/etc/mysql/conf.d/my.cnf
    ports:
      - "3306:3306"
  redis:
    build: ./docker/redis
    container_name: bs-point_redis
    ports:
      - "6379:6379"
  postgresql:
    build: ./docker/postgresql
    container_name: bs-point_postgresql
    ports:
      - "5439:5432"
  flyway-mysql:
    build: ./docker/flyway-mysql
    container_name: bs-point_flyway-mysql
    volumes:
      - ./docker/flyway-mysql/bin:/flyway/bin
      - ./docker/flyway-mysql/conf:/flyway/conf
      - ./docker/flyway-mysql/sql:/flyway/sql
      - ./docker/flyway-mysql/testdata:/flyway/testdata
    depends_on:
      - mysql
    links:
      - mysql
    environment:
      - POINT_DOCKER_TESTDATA=${POINT_DOCKER_TESTDATA}
  flyway-postgresql:
    build: ./docker/flyway-postgresql
    container_name: bs-point_flyway-postgresql
    volumes:
      - ./docker/flyway-postgresql/bin:/flyway/bin
      - ./docker/flyway-postgresql/conf:/flyway/conf
      - ./docker/flyway-postgresql/sql:/flyway/sql
    depends_on:
      - postgresql
    links:
      - postgresql

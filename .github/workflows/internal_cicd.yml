name: Internal deploy workflow

on:
  workflow_call:
    inputs:
      project:
        type: string
        required: true
        description: Deploy project
      target:
        type: string
        required: true
        description: Deploy target environment
    secrets:
     AWS_ACCESS_KEY_ID:
        required: true
     AWS_SECRET_ACCESS_KEY:
        required: true

jobs:
  build:
    environment:
      name: ${{ github.ref_name }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
          cache: 'gradle'

      - name: Set up gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: 7.6

      - name: Environments
        run: |
          CLUSTER_NAME=point
          ENVIRONMENT=${{ inputs.target }}

          case $ENVIRONMENT in
          dev)
            AWS_ROLE_ARN=arn:aws:iam::905418018638:role/custodian-CICDRole
            ;;
          dev2)
            AWS_ROLE_ARN=arn:aws:iam::582127077384:role/custodian-CICDRole
            ;;
          dev3)
            AWS_ROLE_ARN=arn:aws:iam::600477414503:role/custodian-CICDRole
            ;;
          stg)
            AWS_ROLE_ARN=arn:aws:iam::471112755246:role/custodian-CICDRole
            ;;
          prd)
            AWS_ROLE_ARN=arn:aws:iam::211125716602:role/custodian-CICDRole
            ;;
          *)
            echo "Not support env: $ENVIRONMENT"
            exit 1
          esac

          cat >> $GITHUB_ENV <<EOF
          PROJECT_NAME=${{ inputs.project }}
          ENVIRONMENT=$ENVIRONMENT
          CLUSTER_NAME=$CLUSTER_NAME
          AWS_ROLE_ARN=$AWS_ROLE_ARN
          EOF

      - name: Set execute permissions
        run: chmod +x $PROJECT_NAME/../scripts/deploy.sh

      - name: Build
        run: |
          cd $PROJECT_NAME
          ../scripts/deploy.sh $ENVIRONMENT -b

      - name: Cleanup Gradle Cache
        run: |
          rm -f ~/.gradle/caches/modules-2/modules-2.lock
          rm -f ~/.gradle/caches/modules-2/gc.properties

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1.5.10
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          role-duration-seconds: 900
          role-session-name: cicd
          role-skip-session-tagging: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Docker push
        run: |
          cd $PROJECT_NAME
          ../scripts/deploy.sh $ENVIRONMENT -p

      - name: Install kubectl
        uses: azure/setup-kubectl@v1

      - name: K8s restart
        run: |
          aws eks update-kubeconfig --name $CLUSTER_NAME

          cd $PROJECT_NAME
          ../scripts/deploy.sh $ENVIRONMENT -k


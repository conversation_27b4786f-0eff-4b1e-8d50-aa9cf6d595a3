name: On push for point-app

on:
  push:
    branches: [dev, dev2, dev3, dev-app, stg, stg-app, prd, prd-app]
    paths:
      - "point-common/**"
      - "point-app/**"
      - ".github/workflows/point-app.yml"
      - ".github/workflows/internal_cicd.yml"
      - "scripts/**"
      - "ponta/**"

jobs:
  call-workflow:
    uses: ./.github/workflows/internal_cicd.yml
    with:
      project: point-app
      target: ${{ github.ref_name }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.CICD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.CICD_AWS_SECRET_ACCESS_KEY }}

name: On push for point-worker

on:
  push:
    branches: [dev, dev2, dev3, dev-worker, stg, stg-worker, prd, prd-worker]
    paths:
      - "point-common/**"
      - "point-worker/**"
      - ".github/workflows/point-worker.yml"
      - ".github/workflows/internal_cicd.yml"
      - "scripts/**"
      - "ponta/**"

jobs:
  call-workflow:
    uses: ./.github/workflows/internal_cicd.yml
    with:
      project: point-worker
      target: ${{ github.ref_name }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.CICD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.CICD_AWS_SECRET_ACCESS_KEY }}

name: On push for point-api

on:
  push:
    branches: [dev, dev2, dev3, dev-api, stg, stg-api, prd, prd-api]
    paths:
      - "point-common/**"
      - "point-api/**"
      - ".github/workflows/point-api.yml"
      - ".github/workflows/internal_cicd.yml"
      - "scripts/**"
      - "ponta/**"

jobs:
  call-workflow:
    uses: ./.github/workflows/internal_cicd.yml
    with:
      project: point-api
      target: ${{ github.ref_name }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.CICD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.CICD_AWS_SECRET_ACCESS_KEY }}

CREATE TABLE pos_best_price
(
    id         bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    symbol_id  bigint          NOT NULL,
    best_ask   decimal(34, 20) NOT NULL,
    best_bid   decimal(34, 20) NOT NULL,
    created_at timestamp       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT NULL
);

GRANT
ALL
ON pos_best_price TO point;

CREATE TABLE pos_order
(
    id               bigint          NOT NULL,
    symbol_id        bigint          NOT NULL,
    user_id          bigint          NOT NULL,
    order_side       varchar(255)    NOT NULL,
    order_type       varchar(255)    NOT NULL,
    order_channel    varchar(255)    NOT NULL DEFAULT 'UNKNOWN'::character varying,
    price            numeric(34, 20) NULL,
    mm_price         numeric(34, 20) NOT NULL,
    amount           numeric(34, 20) NOT NULL,
    remaining_amount numeric(34, 20) NOT NULL,
    order_status     varchar(255)    NOT NULL,
    order_operator   varchar(255)    NOT NULL,
    created_at       timestamp       NOT NULL DEFAULT 'now'::character varying::timestamp with time zone,
    updated_at       timestamp NULL,
    covered          bool NULL, -- is covered
    notes            varchar(128)    NULL,
    id_type        varchar(34)       NULL,
    CONSTRAINT pos_order_pkey PRIMARY KEY (id)
);
GRANT
ALL
ON pos_order TO point;

CREATE TABLE pos_trade
(
    id             bigint          NOT NULL,
    symbol_id      bigint          NOT NULL,
    user_id        bigint          NOT NULL,
    order_side     varchar(128)    NOT NULL,
    order_type     varchar(128)    NOT NULL,
    order_channel  varchar(128)    NOT NULL DEFAULT 'UNKNOWN'::character varying,
    price          numeric(34, 20) NOT NULL,
    amount         numeric(34, 20) NOT NULL,
    jpy_conversion numeric(34, 20) NOT NULL DEFAULT 0,
    trade_action   varchar(255)    NOT NULL,
    order_id       bigint          NOT NULL,
    fee            numeric(34, 20) NOT NULL,
    asset_amount   numeric(34, 20) NOT NULL DEFAULT 0,
    created_at     timestamp       NOT NULL DEFAULT 'now'::character varying::timestamp with time zone,
    updated_at     timestamp       NULL,
    id_type        varchar(34)     NULL,
    eval_profit_loss_amt numeric(34,20),
    eval_profit_loss_amt_rate numeric(34,20),
    avg_acq_unit_price numeric(34,20),
    income numeric(34,20),
    user_growth_stage_id integer default 0 NULL,
    experience_points        integer       NULL,
    notes varchar(128) NULL,
    CONSTRAINT pos_trade_pkey PRIMARY KEY (id)
);
GRANT
ALL
ON pos_trade TO point;

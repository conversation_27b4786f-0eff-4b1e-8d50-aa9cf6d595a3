aws:
  s3:
    kyc-bucket:
      name: kyc.bs-point-dev
    year-report-bucket:
      name: year-report.bs-point-dev
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
point-app:
  support-email: <EMAIL>
  allowed-origin: https://dev.backseat-service.com
  security:
    enable-invest-login-whitelist: false
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
server:
  servlet:
    session:
      cookie:
        secure: true
spring:
  config:
    domain: dev.backseat-service.com
    environment: dev
  datasource:
    master:
      leak-detection-threshold: 5000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
mail:
  message:
    account-created:
      key: ACCOUNT-CREATED
      base-url: https://dev.backseat-service.com/invest/?cid={0}&token={1}&exp={2}
    forgot-login-password:
      key: FORGOT-LOGIN-PASSWORD
      base-url: https://dev.backseat-service.com/signIn/reset/?cid={0}&token={1}&exp={2}

coin:
  cus:
    host: https://dev.backseat-service.com
    host-external: https://dev.backseat-service.com

ponta:
  login-url: http://************:8080/login # ponta login screen
  callback-url: /oauth
  login-success-url: https://dev.backseat-service.com/oauth/callback/
  login-failure-url: https://dev.backseat-service.com/oauth/callback/
  partner-number: BC202501_00001
  credentials:
  if1507-Url: http://************:8080/ifs/LWIF1507
  if1511-Url: http://************:8080/ifs/LWIF1511
  otp-ttl: 5 # minute
  game-home-url: https://dev.backseat-service.com/farm-game/
  game-login-success-url: https://dev.backseat-service.com/farm-game/farm/
  game-login-failure-url: https://dev.backseat-service.com/farm-game/

point-pos:
  best-price:
    amber:
      api-host: https://be-alpha.whalefin.com
  base-trade:
    amber:
      api-host: https://be-alpha.whalefin.com

swagger:
  enabled: true
springdoc:
  swagger-ui:
    enabled: true
  servers:
    - url: https://dev.backseat-service.com/
  packages-to-scan: point.app.invest.controller,point.app.invest.controller.pontaApi, point.app.operate.controller, point.app.operate.controller.pontaApi,point.app.game.controller
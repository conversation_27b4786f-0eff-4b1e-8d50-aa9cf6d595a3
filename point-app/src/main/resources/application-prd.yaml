aws:
  s3:
    kyc-bucket:
      name: kyc.bs-point-prd
    year-report-bucket:
      name: year-report.bs-point-prd
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
point-app:
  support-email: <EMAIL>
  allowed-origin: https://backseat-service.com
  security:
    enable-invest-login-whitelist: false
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
server:
  servlet:
    session:
      cookie:
        secure: true
spring:
  config:
    domain: backseat-service.com
    environment: prd
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
mail:
  message:
    account-created:
      key: ACCOUNT-CREATED
      base-url: https://backseat-service.com/invest/?cid={0}&token={1}&exp={2}
    forgot-login-password:
      key: FORGOT-LOGIN-PASSWORD
      base-url: https://backseat-service.com/signIn/reset/?cid={0}&token={1}&exp={2}
coin:
  cus:
    host: https://backseat-service.com
    host-external: https://backseat-service.com

ponta:
  login-url: https://www.ponta.jp/front/LWAS900/SLWAS900110.htm
  callback-url: /oauth
  login-success-url: https://backseat-service.com/oauth/callback/
  login-failure-url: https://backseat-service.com/oauth/callback/  
  partner-number: BC202505_00001
  credentials:
  if1507-Url: https://onlinesx02.lw.loyalty.co.jp:28443/ifs/LWIF1507
  if1511-Url: https://onlinesx02.lw.loyalty.co.jp:28443/ifs/LWIF1511
  webIf-Url: https://onlinesx01.lp.loyalty.co.jp:8443/webif/bizinvoker
  otp-ttl: 5 # minute
  game-home-url: https://backseat-service.com/farm-game/
  game-login-success-url: https://backseat-service.com/farm-game/farm/
  game-login-failure-url: https://backseat-service.com/farm-game/

point-pos:
  best-price:
    amber:
      api-host: https://be.whalefin.com
  base-trade:
    amber:
      api-host: https://be.whalefin.com

swagger:
  enabled: false
springdoc:
  swagger-ui:
    enabled: false
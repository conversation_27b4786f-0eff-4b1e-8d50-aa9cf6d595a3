package point.app.component.jwt;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.common.collect.Maps;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import point.app.component.model.UserPrincipal;
import point.common.component.RedisManager;
import point.common.constant.CommonConstants;
import point.common.constant.UserIdType;
import point.common.service.UserIdentityService;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public final class JwtManager {

    // -- static variables

    public static final String TOKEN = "token";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String SCOPE_ACCESS = "access";
    public static final String SCOPE_REFRESH = "refresh";
    public static final String SCOPE = "scope";
    private final RedisManager redisManager;
    private final UserIdentityService userIdentityService;

    // -- instance variables

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.issuer:cb-exchange-server}")
    private String issuer;

    @Value("${jwt.token-ttl:7600}")
    private int tokenTtl;

    @Value("${refresh-token-ttl:604800}")
    private int refreshTokenTtl;

    @Value("${cache-token-ttl:604800}")
    private int cacheTokenTtl;

    // -- instance methods

    /**
     * create token pair
     *
     * @param subject alias user name
     * @param userFn alias claim
     * @return {"token": "...", "refreshToken": "..."}
     */
    public Map<String, String> createTokenPair(String subject, Supplier<UserPrincipal> userFn) {
        Map<String, ?> payload = this.buildPayload(userFn.get());
        String token = this.createJwt(SCOPE_ACCESS, subject, payload, tokenTtl);
        String refreshToken = this.createJwt(SCOPE_REFRESH, subject, payload, refreshTokenTtl);
        return Map.of(TOKEN, token, REFRESH_TOKEN, refreshToken);
    }

    /**
     * generic a new token by refreshToken
     *
     * @param refreshToken refresh token
     * @return access token
     */
    public Map<String, String> genericToken(
            String refreshToken, BiFunction<Long, UserIdType, UserPrincipal> userFn) {
        DecodedJWT decodedJWT = this.decodedJWT(refreshToken);
        if (decodedJWT == null) {
            throw new IllegalArgumentException("The refreshToken invalid");
        }

        Map<String, Claim> claims = decodedJWT.getClaims();
        Claim scopeClaim = claims.get(SCOPE);
        if (scopeClaim == null) {
            throw new IllegalArgumentException("The scope of refreshToken invalid");
        }

        String scope = scopeClaim.asString();
        if (!scope.equals(SCOPE_REFRESH)) {
            throw new IllegalArgumentException("The scope of refreshToken invalid");
        }

        Claim userIdClaim = claims.get(CommonConstants.USER_ID);
        if (userIdClaim == null) {
            throw new IllegalArgumentException("The userId not found in refreshToken");
        }

        Claim userIdTypeClaim = claims.get(CommonConstants.ID_TYPE);
        if (userIdTypeClaim == null) {
            throw new IllegalArgumentException("The userIdType not found in refreshToken");
        }

        Map<String, String> tokenPair =
                this.createTokenPair(
                        decodedJWT.getSubject(),
                        () ->
                                userFn.apply(
                                        userIdClaim.asLong(),
                                        UserIdType.valueOf(userIdTypeClaim.asString())));
        String key = CommonConstants.REDIS_USER_TOCKN_PREFIX + userIdClaim.asLong();
        String redisToken = redisManager.hget(key, JwtManager.TOKEN);
        if (StringUtils.isNotBlank(redisToken)) {
            redisManager.delete(key);
        }
        redisManager.hset(key, tokenPair, cacheTokenTtl);
        return tokenPair;
    }

    /**
     * create jwt
     *
     * @param subject alias username
     * @param payload alias claim
     * @param ttl expire second
     * @return jwt token
     */
    public String createJwt(String scope, String subject, Map<String, ?> payload, int ttl) {
        Algorithm algorithm = Algorithm.HMAC512(secret);

        Date issuedAt = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(issuedAt);
        calendar.add(Calendar.SECOND, ttl);

        return JWT.create()
                .withIssuer(issuer)
                // .withSubject(subject)
                .withIssuedAt(issuedAt)
                .withExpiresAt(calendar.getTime())
                .withPayload(payload)
                .withClaim(SCOPE, scope)
                .sign(algorithm);
    }

    /**
     * @param token jwt token
     * @return DecodedJWT
     */
    public DecodedJWT decodedJWT(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC512(secret);
            JWTVerifier verifier =
                    JWT.require(algorithm)
                            // specify an specific claim validations
                            .withIssuer(issuer)
                            .build();

            return verifier.verify(token);
        } catch (JWTVerificationException e) {
            // Invalid signature/claims
            if (log.isDebugEnabled()) {
                log.debug("jwt token verify fail: {}", e.getMessage());
            }
            return null;
        }
    }

    public boolean validateToken(String token) {
        DecodedJWT decodedJWT = this.decodedJWT(token);
        return decodedJWT != null;
    }

    public boolean isAccessToken(Map<String, Claim> claimMap) {
        if (!claimMap.containsKey(SCOPE)) {
            return false;
        }
        Claim scopeClaim = claimMap.get(SCOPE);
        return scopeClaim != null && SCOPE_ACCESS.equals(scopeClaim.asString());
    }

    // -- private methods

    private Map<String, Object> buildPayload(UserPrincipal principal) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(5);
        Long id = principal.getId();
        UserIdType userIdType = principal.getUserIdType();
        map.put(CommonConstants.USER_ID, id);
        map.put(CommonConstants.ID_TYPE, userIdType.toString());
        return map;
    }
}

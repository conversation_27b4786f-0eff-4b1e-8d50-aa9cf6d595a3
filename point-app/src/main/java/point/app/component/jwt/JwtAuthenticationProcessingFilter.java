package point.app.component.jwt;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import point.app.component.model.UserPrincipal;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;

@Slf4j
public class JwtAuthenticationProcessingFilter extends AbstractAuthenticationProcessingFilter {

    private static final OrRequestMatcher COMMON_WHITE_LIST_REQUEST_MATCHER =
            new OrRequestMatcher(
                    new AntPathRequestMatcher("/app/v1/game/choice/info"),
                    new AntPathRequestMatcher("/app/v1/game/choice/vote"),
                    new AntPathRequestMatcher("/app/v1/game/choice/self-status"),
                    new AntPathRequestMatcher("/app/v1/game/choice/vote-history"),
                    new AntPathRequestMatcher("/app/v1/game/choice/reward-history"),
                    new AntPathRequestMatcher("/app/v1/game/choice/reward-x-connect"),
                    new AntPathRequestMatcher("/app/v1/game/choice/reward-withdraw"),
                    new AntPathRequestMatcher("/app/v1/game/quiz/questions"),
                    new AntPathRequestMatcher("/app/v1/game/quiz/submit"),
                    new AntPathRequestMatcher("/app/v1/game/campaigns"),
                    new AntPathRequestMatcher("/app/v1/game/account-info"),
                    new AntPathRequestMatcher("/app/v1/game/point-balance"));
    private static final AntPathRequestMatcher OPERATE_REQUEST_PATH_MATCHER =
            new AntPathRequestMatcher("/app/v1/operate/**");

    public JwtAuthenticationProcessingFilter(
            AuthenticationManager authenticationManager, RequestMatcher matcher) {
        super(matcher);
        setAuthenticationManager(authenticationManager);
    }

    @Override
    public Authentication attemptAuthentication(
            HttpServletRequest request, HttpServletResponse response) {
        String tokenPayload = request.getHeader(JwtTokenUtil.HEADER_NAME);
        String token = JwtTokenUtil.extractToken(tokenPayload);
        Authentication authenticate =
                getAuthenticationManager().authenticate(new JwtAuthenticationToken(token));
        UserPrincipal principal = (UserPrincipal) authenticate.getPrincipal();

        if (COMMON_WHITE_LIST_REQUEST_MATCHER.matches(request)) {
            return authenticate;
        }

        // deny access operate resources for invest user
        if (UserIdType.Invest.equals(principal.getUserIdType())
                && OPERATE_REQUEST_PATH_MATCHER.matches(request)) {
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_USER_PERMISSION_DENY.getCode()));
        }

        // deny access invest resources for operate user
        if (UserIdType.Operate.equals(principal.getUserIdType())
                && !OPERATE_REQUEST_PATH_MATCHER.matches(request)) {
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_USER_PERMISSION_DENY.getCode()));
        }
        return authenticate;
    }

    @Override
    protected void successfulAuthentication(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain chain,
            Authentication authResult)
            throws IOException, ServletException {
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authResult);
        SecurityContextHolder.setContext(context);
        chain.doFilter(request, response);
    }
}

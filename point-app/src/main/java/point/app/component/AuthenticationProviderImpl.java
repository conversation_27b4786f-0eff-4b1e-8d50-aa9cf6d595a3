package point.app.component;

import com.auth0.jwt.interfaces.Claim;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;
import point.app.component.jwt.JwtManager;
import point.app.component.jwt.JwtTokenUtil;
import point.app.component.model.UserPrincipal;
import point.app.component.model.UserWrapper;
import point.app.invest.model.request.UserLoginPostForm;
import point.common.component.RedisManager;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.exception.CustomException;
import point.common.service.LoginAttemptService;
import point.common.service.PointUserService;
import point.common.service.UserService;

/** Invest user login logic handler */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class AuthenticationProviderImpl implements AuthenticationProvider {

    private final JwtManager jwtManager;
    private final RedisManager redisManager;
    private final MfaManager mfaManager;
    private final RecaptchaManager recaptchaManager;
    private final UserService userService;
    private final LoginAttemptService loginAttemptService;
    private final PointUserService pointUserService;

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException {
        UserLoginPostForm userLoginPostForm = (UserLoginPostForm) authentication.getPrincipal();

        // -- start check ponta(operate user) token
        String token = (String) authentication.getCredentials();
        Map<String, Claim> claims = JwtTokenUtil.getClaim(jwtManager, token);
        if (!JwtTokenUtil.isAccessToken(jwtManager, claims)) {
            log.info("jwt token scope invalid");
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }

        Claim claim = claims.get(CommonConstants.USER_ID);
        if (Objects.isNull(claim) || Objects.isNull(claim.asLong())) {
            log.info("jwt token cannot have a valid user_id");
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        // operate userId
        Long userId = claim.asLong();
        String redisToken = JwtTokenUtil.getJwtFromRedis(redisManager, userId);
        if (!StringUtils.hasText(redisToken)) {
            log.info("The JWT token does not exist in redis : {}", userId);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        if (!token.equals(redisToken)) {
            log.info("The JWT tokens are inconsistent in redis : {}", userId);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        // -- end check ponta(operate user)token

        if (userLoginPostForm == null) {
            throw new BadCredentialsException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_LOGIN_INFO_IS_NULL.getCode()));
        }

        User user = userService.findByEmail(userLoginPostForm.getEmail());
        if (user == null) {
            throw new BadCredentialsException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_USER_IS_NULL.getCode()));
        }

        if (!user.isAccountNonLocked() && !loginAttemptService.isLockExpired(user.getId())) {
            throw new LockedException(
                    Integer.toString(
                            ErrorCode.REQUEST_ERROR_UNAUTHORIZED_ACCOUNT_LOCKED.getCode()));
        } else if (!user.isAllowedToLogin()) {
            throw new DisabledException(
                    Integer.toString(ErrorCode.AUTHENTICATION_ERROR_ACCOUNT_STOPPED.getCode()));
        }

        if (!new BCryptPasswordEncoder()
                .matches(userLoginPostForm.getPassword(), user.getPassword())) {
            if (loginAttemptService.countUp(user.getId())) {
                user.setAccountNonLocked(false);
                userService.saveWithAuthenticationPrincipal(user);
            }

            throw new BadCredentialsException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_INVALID_USER_PASSWORD.getCode()));
        }

        try {
            mfaManager.authenticate(user.getId(), user.getEmail(), userLoginPostForm.getMfaCode());
        } catch (Exception e) {
            if (e instanceof CustomException) {
                throw new BadCredentialsException(
                        Integer.toString(((CustomException) e).getErrorCode().getCode()));
            } else {
                throw new BadCredentialsException(
                        Integer.toString(ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getCode()));
            }
        }

        try {
            recaptchaManager.verify(userLoginPostForm.getRecaptchaToken());
        } catch (Exception e) {
            throw new BadCredentialsException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_INVALID_RECAPTCHA_TOKEN.getCode()));
        }

        try {
            // binding relation with operate user
            pointUserService.bindRelationWithSyncPowerChoice(userId, user.getId());
        } catch (Exception e) {
            if (e instanceof CustomException ce) {
                throw new AuthenticationServiceException(
                        Integer.toString(ce.getErrorCode().getCode()));
            }
            log.error(
                    "Failed to binding relation between operate user({}) with invest user({})",
                    userId,
                    user.getId());
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getCode()));
        }

        PointUser pointUser = pointUserService.findByUserId(user.getId());
        return new UsernamePasswordAuthenticationToken(
                UserPrincipal.from(
                        user.getId(),
                        UserIdType.Invest,
                        user.getEmail(),
                        UserWrapper.builder().user(user).pointUser(pointUser).build()),
                userLoginPostForm.getPassword(),
                new ArrayList<>());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

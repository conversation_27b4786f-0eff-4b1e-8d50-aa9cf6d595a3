package point.app.component;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.authentication.session.ChangeSessionIdAuthenticationStrategy;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import point.app.component.jwt.JwtTokenUtil;
import point.app.invest.model.request.UserLoginPostForm;

/** Receive invest user login request */
@Slf4j
public class AuthenticationProcessingFilter extends AbstractAuthenticationProcessingFilter {

    protected AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource =
            new WebAuthenticationDetailsSource();

    public AuthenticationProcessingFilter(AuthenticationManager authenticationManager) {
        super(new AntPathRequestMatcher("/app/v1/user/login", "POST"));
        setAuthenticationManager(authenticationManager);
        setSessionAuthenticationStrategy(new ChangeSessionIdAuthenticationStrategy());
    }

    @Override
    public Authentication attemptAuthentication(
            HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException, IOException, ServletException {
        UserLoginPostForm userLoginPostForm =
                new ObjectMapper().readValue(request.getInputStream(), UserLoginPostForm.class);

        String tokenPayload = request.getHeader(JwtTokenUtil.HEADER_NAME);
        String token = JwtTokenUtil.extractToken(tokenPayload);

        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                new UsernamePasswordAuthenticationToken(userLoginPostForm, token);

        usernamePasswordAuthenticationToken.setDetails(
                authenticationDetailsSource.buildDetails(request));
        return getAuthenticationManager().authenticate(usernamePasswordAuthenticationToken);
    }
}

package point.app.game.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.app.component.model.UserPrincipal;
import point.common.constant.*;
import point.common.entity.*;
import point.common.exception.GameException;
import point.common.model.request.RecordShareForm;
import point.common.model.request.RewardShareForm;
import point.common.model.request.WithdrawForm;
import point.common.model.response.ChoiceRewardResponseData;
import point.common.model.response.GlobalApiResponse;
import point.common.service.ChoiceActivityRuleService;
import point.common.service.ChoiceRewardService;
import point.common.service.ChoiceRewardWithdrawalService;
import point.common.service.ChoiceVoteService;
import point.common.service.SharedHistService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/game/choice")
public class ChoiceRewardController {

    private final ChoiceVoteService choiceVoteService;

    private final ChoiceRewardWithdrawalService choiceRewardWithdrawalService;
    private final ChoiceRewardService choiceRewardService;

    private final SharedHistService sharedHistService;

    private final ChoiceActivityRuleService choiceActivityRuleService;

    @GetMapping("/reward-history")
    @Operation(
            summary = "choice reward history",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<ChoiceRewardResponseData>> getQuizQuestion(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam("yearMonth") Integer yearMonth) {
        Pair<Long, Long> userIdPair = extractUserIds(userPrincipal);
        return choiceVoteService.getUserVoteRewardHistory(
                userPrincipal.getUserIdType(), userIdPair, yearMonth);
    }

    /**
     * 報酬履歴画面_Xへのシェア処理
     *
     * @param user the login user
     * @param form query params
     * @return GlobalApiResponse<Long>
     * @throws Exception
     */
    @PostMapping("/reward-x-connect")
    @Operation(
            summary = "share reward history to X",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<Long>> shareRecordToX(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user,
            @RequestBody RewardShareForm form)
            throws Exception {
        if (Objects.isNull(form.getRewardId())) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_REWARD_NOT_FOUND.getMessage()));
        }

        ChoiceReward choiceReward = choiceRewardService.findOne(form.getRewardId());
        if (Objects.isNull(choiceReward)) {
            log.info("there is no choice reward record by id {}", form.getRewardId());
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_REWARD_NOT_FOUND.getMessage()));
        }

        if (!user.getUserIds().contains(choiceReward.getUserId())) {
            log.info(
                    "can not share other's record : id={}, userIds: {}",
                    form.getRewardId(),
                    user.getUserIds());
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_REWARD_NOT_FOUND.getMessage()));
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        if (choiceReward.getRewardTime().before(calendar.getTime())) {
            log.info(
                    "can not share the record(only can share the choice reward of yesterday) : id={}",
                    form.getRewardId());
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_REWARD_NOT_FOUND.getMessage()));
        }

        ChoiceVote choiceVote = choiceVoteService.findOneVoteByReward(choiceReward);
        if (Objects.isNull(choiceVote)
                || !Objects.equals(ChoiceVoteResult.WIN, choiceVote.getVoteResult())) {
            log.info("not win reward record : id={}", form.getRewardId());
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_REWARD_NOT_FOUND.getMessage()));
        }

        return ResponseEntity.ok()
                .body(
                        sharedHistService.shareRecordAndJoinCampaign(
                                RecordShareForm.builder().recordId(form.getRewardId()).build(),
                                user.getId(),
                                SharedRecordType.REWARD_HIST_SHARED));
    }

    @PostMapping("/reward-withdraw")
    @Operation(
            summary = " reward withdraw",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<GlobalApiResponse<String>> rewardWithdraw(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody() WithdrawForm form)
            throws Exception {
        Pair<Long, Long> userIdPair = extractUserIds(userPrincipal);
        try {
            WithdrawType withdrawType = WithdrawType.fromString(form.getWithdrawType());
            return choiceRewardWithdrawalService.rewardWithdrawal(
                    userIdPair,
                    form.getChoicePAmount(),
                    withdrawType,
                    userPrincipal.getUserIdType());
        } catch (IllegalArgumentException e) {
            throw new GameException(
                    ErrorCode.GAME_INVALID_WITHDRAW_TYPE,
                    ErrorCode.GAME_INVALID_WITHDRAW_TYPE.getMessage());
        }
    }

    private Pair<Long, Long> extractUserIds(UserPrincipal userPrincipal) {
        Long investUserId = null;
        Long operatorUserId = null;

        if (userPrincipal.getUserIdType() == UserIdType.Invest) {
            investUserId = userPrincipal.getId(); // user Table
            PointUser pointUser = userPrincipal.getUserWrapper().getPointUser();
            if (Objects.nonNull(pointUser) && Objects.nonNull(pointUser.getId())) {
                operatorUserId = pointUser.getId();
            }
        }

        if (userPrincipal.getUserIdType() == UserIdType.Operate) {
            operatorUserId = userPrincipal.getId();
            User user = userPrincipal.getUserWrapper().getUser();
            if (Objects.nonNull(user) && Objects.nonNull(user.getId())) {
                investUserId = user.getId();
            }
        }
        return Pair.of(investUserId, operatorUserId);
    }
}

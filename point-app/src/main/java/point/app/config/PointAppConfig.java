package point.app.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "point-app")
public class PointAppConfig {

    @Getter @Setter private String noReplyEmail;

    @Getter @Setter private String supportEmail;

    @Getter @Setter private String allowedOrigin;
}

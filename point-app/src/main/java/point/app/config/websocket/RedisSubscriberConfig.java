package point.app.config.websocket;

import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
@AllArgsConstructor
@ConditionalOnProperty(
        name = "exchange-websocket.redis-pubsub-subscriber.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class RedisSubscriberConfig {

    private final RedisSubscriber messageListener;
    private final RedisConnectionFactory connectionFactory;

    // 取引所　顧客残高表示
    private final ChannelTopic assetTopic;
    // 取引所　Currency Pair
    private final ChannelTopic currencyPairTopic;
    // 販売所　チャート表示
    private final ChannelTopic posCandlestickTopic;
    // 販売所　注文パネル
    private final ChannelTopic posPriceTopic;
    // 販売所　約定一覧
    private final ChannelTopic posTradeUpdateTopic;

    private final ChannelTopic choiceActivityTopic;

    @Bean
    public RedisMessageListenerContainer redisContainer() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(messageListener, assetTopic);
        container.addMessageListener(messageListener, currencyPairTopic);
        container.addMessageListener(messageListener, posCandlestickTopic);
        container.addMessageListener(messageListener, posPriceTopic);
        container.addMessageListener(messageListener, posTradeUpdateTopic);
        container.addMessageListener(messageListener, choiceActivityTopic);
        return container;
    }
}

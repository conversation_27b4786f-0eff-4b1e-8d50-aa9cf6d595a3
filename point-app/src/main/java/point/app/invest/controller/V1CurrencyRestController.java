package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.config.IconConfig;
import point.common.constant.Currency;
import point.common.constant.TradeType;
import point.common.controller.AbstractRestController;
import point.common.model.response.CurrencyData;
import point.common.service.CurrencyConfigService;

@Hidden
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/currency")
@Timed
public class V1CurrencyRestController extends AbstractRestController {

    private final CurrencyConfigService currencyConfigService;
    private final IconConfig iconConfig;

    @GetMapping
    public ResponseEntity<List<CurrencyData>> get(HttpServletResponse response) {
        setCacheControlForPublic(response);

        // 有効な(enabled=true)通貨のみ
        // すべて無効の場合は空リストを返す
        return ResponseEntity.ok(
                currencyConfigService.findAllByCondition(TradeType.INVEST, null, true).stream()
                        .map(
                                currencyConfig ->
                                        new CurrencyData(
                                                currencyConfig,
                                                Currency.valueOf(
                                                                currencyConfig.getCurrency().name())
                                                        .getPrecision(),
                                                iconConfig.getIconUrl(
                                                        currencyConfig.getCurrency().name())))
                        .collect(Collectors.toList()));
    }
}

package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.exception.CustomException;
import point.common.model.request.UserMailNoticesForm;
import point.common.service.UserMailNoticesOffService;

@Hidden
@Timed
@RestController
@RequestMapping("/app/v1/mail-notices")
@RequiredArgsConstructor
public class V1UserMailNoticesOffController {

    private final UserMailNoticesOffService userMailNoticesOffService;

    @PostMapping
    public ResponseEntity<Object> put(
            @AuthenticationPrincipal UserPrincipal user,
            @Validated @RequestBody UserMailNoticesForm userMailNoticesForm)
            throws CustomException {
        userMailNoticesForm.setUserId(user.getId());
        userMailNoticesOffService.save(userMailNoticesForm);
        return ResponseEntity.ok().build();
    }
}

package point.app.invest.controller;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.jwt.JwtManager;
import point.app.component.model.UserPrincipal;
import point.app.component.model.UserWrapper;
import point.common.component.RedisManager;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.exception.RefreshTokenException;
import point.common.service.PointUserService;
import point.common.service.UserService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/refresh-token")
@Timed
public class V1RefreshTokenController {

    private final JwtManager jwtManager;
    private final UserService userService;
    private final PointUserService pointUserService;
    private final RedisManager redisManager;

    @Operation(
            summary = "Refresh JWT Token",
            description =
                    "This endpoint refreshes a JWT token using a valid refresh token. "
                            + "The refresh token must be provided in the request body. "
                            + "If the refresh token is valid, a new JWT token is generated and returned. "
                            + "If the refresh token is invalid or expired, an error is returned.",
            requestBody =
                    @io.swagger.v3.oas.annotations.parameters.RequestBody(
                            description =
                                    "A map containing the refresh token and the current JWT token. "
                                            + "The refresh token is required to generate a new JWT token.",
                            required = true,
                            content =
                                    @Content(
                                            mediaType = "application/json",
                                            examples =
                                                    @ExampleObject(
                                                            value =
                                                                    "{\"refreshToken\": \"your_refresh_token\", \"token\": \"your_jwt_token\"}"))),
            responses = {
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "200",
                        description = "Successfully refreshed the JWT token",
                        content =
                                @Content(
                                        mediaType = "application/json",
                                        examples =
                                                @ExampleObject(
                                                        value =
                                                                "{\"token\": \"new_jwt_token\", \"refreshToken\": \"new_refresh_token\"}"))),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "400",
                        description =
                                "Bad Request. The request body is missing the refresh token or the token is invalid.",
                        content =
                                @Content(
                                        mediaType = "application/json",
                                        examples =
                                                @ExampleObject(
                                                        value =
                                                                "{\"code\": 10001, \"message\": null, \"params\": null}"))),
                @io.swagger.v3.oas.annotations.responses.ApiResponse(
                        responseCode = "401",
                        description = "Unauthorized. The refresh token or the token is invalid.",
                        content =
                                @Content(
                                        mediaType = "application/json",
                                        examples =
                                                @ExampleObject(
                                                        value =
                                                                "{\"code\": 70122, \"message\": null, \"params\": null}")))
            })
    @PostMapping
    public ResponseEntity<Map<String, String>> refreshToken(
            @RequestBody Map<String, String> tokenMap) throws RefreshTokenException {

        if (CollectionUtils.isEmpty(tokenMap) || !tokenMap.containsKey(JwtManager.REFRESH_TOKEN)) {
            throw new RefreshTokenException(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE);
        }

        String refreshToken = tokenMap.get(JwtManager.REFRESH_TOKEN);
        String token = tokenMap.get(JwtManager.TOKEN);
        log.info("this refresh token: {}", refreshToken);
        log.info("this jwt token: {}", token);
        try {
            if (StringUtils.isNotEmpty(refreshToken)) {
                DecodedJWT decodedJWT = jwtManager.decodedJWT(refreshToken);
                if (decodedJWT == null) {
                    log.info("jwt refreshToken cannot parse: {}", token);
                    throw new AuthenticationServiceException(
                            Integer.toString(
                                    ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
                }
                Map<String, Claim> claims = decodedJWT.getClaims();
                Claim claim = claims.get(CommonConstants.USER_ID);
                Long userId = claim.asLong();
                String key = CommonConstants.REDIS_USER_TOCKN_PREFIX + userId;
                String oldRedisToken = redisManager.hget(key, JwtManager.REFRESH_TOKEN);
                if (!refreshToken.equals(oldRedisToken)) {
                    throw new RefreshTokenException(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE);
                }
            }

            return ResponseEntity.ok(
                    jwtManager.genericToken(
                            refreshToken,
                            (id, idType) -> {
                                if (UserIdType.Invest.equals(idType)) {
                                    User user = userService.findOne(id);
                                    PointUser pointUser = pointUserService.findByUserId(id);
                                    return UserPrincipal.from(
                                            id,
                                            UserIdType.Invest,
                                            user.getEmail(),
                                            UserWrapper.builder()
                                                    .user(user)
                                                    .pointUser(pointUser)
                                                    .build());
                                } else {
                                    PointUser user = pointUserService.findOne(id);
                                    User investUser = null;
                                    if (Objects.nonNull(user.getUserId())) {
                                        investUser = new User();
                                        investUser.setId(user.getUserId());
                                    }
                                    return UserPrincipal.from(
                                            id,
                                            UserIdType.Operate,
                                            null,
                                            UserWrapper.builder()
                                                    .pointUser(user)
                                                    .user(investUser)
                                                    .build());
                                }
                            }));
        } catch (IllegalArgumentException e) {
            log.warn("refresh token error: {}", e.getMessage());
            throw new RefreshTokenException(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE);
        }
    }
}

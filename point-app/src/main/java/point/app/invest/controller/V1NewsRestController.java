package point.app.invest.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.entity.UserNews;
import point.common.model.request.NewsReadForm;
import point.common.model.response.NewsData;
import point.common.model.response.UserNewsData;
import point.common.service.NewsService;
import point.common.service.UserNewsService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/news")
@Timed
public class V1NewsRestController {
    private final NewsService newsService;
    private final UserNewsService userNewsService;

    @GetMapping
    @Operation(
            summary = "get news",
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    public ResponseEntity<NewsData> get() {
        NewsData newsData = new NewsData();
        newsData.newsList(newsService.findByCondition());
        return ResponseEntity.ok(newsData);
    }

    @GetMapping("/read")
    public ResponseEntity<UserNewsData> getRead(@AuthenticationPrincipal UserPrincipal user) {
        UserNews userNews = (userNewsService.findByUserId(user.getId()));

        UserNewsData data = new UserNewsData();
        ArrayList<Long> readIdList = new ArrayList<Long>();
        for (String id : userNews.getReadIds().split(",")) {
            readIdList.add(Long.valueOf(id));
        }
        data.setReadIds(readIdList.toArray(new Long[readIdList.size()]));

        return ResponseEntity.ok(data);
    }

    @PostMapping("/read")
    public ResponseEntity<Object> postRead(
            @AuthenticationPrincipal UserPrincipal user, @RequestBody NewsReadForm form) {
        userNewsService.saveReadId(user.getId(), form.getNewsId());
        return ResponseEntity.ok(1);
    }
}

package point.app.invest.model.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

public class BankAccountOtpauthPostForm {

    @Getter @Setter @NotNull private Long bankId;

    @Getter @Setter @NotNull private String accountType;

    @Getter @Setter @NotNull private String accountNumber;

    @Getter @Setter @NotNull @NotEmpty private String accountName;

    @JsonIgnore
    @AssertTrue
    public boolean isValidAccountNumber() {
        if (StringUtils.isEmpty(accountNumber)) {
            return false;
        }
        if (accountNumber.length() != 7) {
            return false;
        }
        if (!NumberUtils.isParsable(accountNumber)) {
            return false;
        }
        return true;
    }
}

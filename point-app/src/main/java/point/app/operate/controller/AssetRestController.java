package point.app.operate.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.app.component.model.UserPrincipal;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.exception.GameException;
import point.common.model.response.AssetAllData;
import point.common.model.response.AssetData;
import point.common.model.response.AssetResponseApi;
import point.common.model.response.GlobalApiResponse;
import point.common.service.*;
import point.common.util.CalculatorUtil;
import point.pos.model.PosBestPriceData;
import point.pos.service.PosBestPriceService;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/operate/asset")
@Timed
public class AssetRestController {

    private final AssetService assetService;
    private final AssetSummaryService assetSummaryService;
    private final OrderbookService orderbookService;
    private final CurrencyConfigService currencyConfigService;
    private final PointUserService pointUserService;
    private final PosTradeService posTradeService;
    private final SymbolService symbolService;
    private final ChoicePowerService choicePowerService;
    private final ChoicePowerUserRelService choicePowerUserRelService;
    private final PosBestPriceService posBestPriceService;
    private static final int SCALE_3 = 3;
    private static final int SCALE_2 = 2;
    private static final int SCALE_4 = 4;

    @GetMapping
    @Operation(
            summary = "operate get asset",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
            })
    public ResponseEntity<List<AssetData>> get(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user)
            throws Exception {
        List<AssetData> assetDatas = new ArrayList<>();

        // 有効な(enabled=true)通貨の資産を取得
        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(TradeType.OPERATE, null, true);
        if (CollectionUtils.isEmpty(currencyConfigs)) {
            throw new GameException(
                    ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND,
                    ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND.getMessage());
        }
        PointUser pointUser = pointUserService.findOne(user.getId());
        for (CurrencyConfig currencyConfig : currencyConfigs) {
            // Asset無しの場合は初期値で作成
            Asset asset = assetService.findOrCreate(user.getId(), currencyConfig.getCurrency());

            // 板が空の場合は0
            BigDecimal jpyConversion =
                    orderbookService.getOperateBestBidOfQuoteJpy(asset.getCurrency());
            AssetData assetData = getAssetData(asset, jpyConversion, pointUser);
            assetDatas.add(assetData);
        }
        processAsset(assetDatas);
        return ResponseEntity.ok(assetDatas);
    }

    private AssetData getAssetData(Asset asset, BigDecimal jpyConversion, PointUser pointUser) {
        AssetData assetData = new AssetData();
        assetData.setCurrency(asset.getCurrency());
        assetData.setJpyLockedAmount(asset.getLockedAmount().multiply(jpyConversion));
        assetData.setJpyOnhandAmount(
                asset.getOnhandAmount()
                        .multiply(jpyConversion)
                        .setScale(SCALE_2, BigDecimal.ROUND_HALF_UP));
        assetData.setLockedAmount(asset.getLockedAmount());
        assetData.setOnhandAmount(asset.getOnhandAmount());
        assetData.setUnlockedAmount(asset.getOnhandAmount().subtract(asset.getLockedAmount()));
        assetData.setPointUser(pointUser);
        assetData.setUserId(asset.getUserId());
        // find  and cal EvalProfitLossAmt
        Symbol symbolTemp =
                symbolService.findByCondition(
                        TradeType.OPERATE, CurrencyPair.valueOf(asset.getCurrency(), Currency.JPY));
        log.info("[OPERATE] asset symbolTemp: {}", symbolTemp);
        if (symbolTemp != null) {
            PosBestPriceData bestPrice = posBestPriceService.getBestPrice(symbolTemp.getId());
            BigDecimal nowPrice = BigDecimal.ZERO;
            if (bestPrice != null) {
                nowPrice = bestPrice.getBestBid();
            }
            BigDecimal onhandAmount = asset.getOnhandAmount();
            BigDecimal avgAcqUnitPrice = asset.getAvgAcqUnitPrice();
            if (onhandAmount == null) {
                onhandAmount = BigDecimal.ZERO;
            }
            if (avgAcqUnitPrice == null) {
                avgAcqUnitPrice = nowPrice.setScale(SCALE_3, BigDecimal.ROUND_HALF_UP);
            }
            if (onhandAmount.compareTo(BigDecimal.ZERO) == 0) {
                avgAcqUnitPrice = BigDecimal.ZERO;
            }
            BigDecimal totalAssetPrice = asset.getAssetTotal();
            if (totalAssetPrice == null) {
                totalAssetPrice = BigDecimal.ZERO;
            }

            BigDecimal priceDifference = nowPrice.subtract(avgAcqUnitPrice);
            BigDecimal evalProfitLossAmt =
                    onhandAmount.multiply(priceDifference).setScale(SCALE_2, RoundingMode.HALF_UP);
            log.info(
                    "priceDifference: {}, evalProfitLossAmt{},currency:{}",
                    priceDifference,
                    evalProfitLossAmt,
                    asset.getCurrency());
            BigDecimal evalProfitLossAmtRate = BigDecimal.ZERO;
            if (totalAssetPrice.compareTo(BigDecimal.ZERO) != 0) {
                evalProfitLossAmtRate =
                        evalProfitLossAmt.divide(totalAssetPrice, SCALE_4, RoundingMode.HALF_UP);
            }
            assetData.setAssetTotal(totalAssetPrice);
            assetData.setEvalProfitLossAmt(evalProfitLossAmt);
            assetData.setEvalProfitLossAmtRate(evalProfitLossAmtRate);
            assetData.setAvgAcqUnitPrice(avgAcqUnitPrice.setScale(SCALE_3, RoundingMode.HALF_UP));
            assetData.setNowPrice(nowPrice);
            // Calculate your points based on the price and your currency holdings(with out JPY).
            assetData.setOperatePoints(
                    onhandAmount
                            .multiply(avgAcqUnitPrice)
                            .add(evalProfitLossAmt)
                            .setScale(SCALE_2, RoundingMode.HALF_UP));
        }
        return assetData;
    }

    @GetMapping("/all")
    @Operation(
            summary = "operate asset getAll",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
            })
    public ResponseEntity<AssetAllData> getAll(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user)
            throws Exception {
        AssetAllData assetAllData = new AssetAllData();
        assetAllData.setAssets(new ArrayList<>());

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        Map<Currency, AssetSummary> assetSummaryMap = new HashMap<>();

        for (AssetSummary assetSummary :
                assetSummaryService.findByCondition(user.getId(), null, calendar.getTime(), null)) {
            AssetSummary existingAssetSummary = assetSummaryMap.get(assetSummary.getCurrency());

            if (existingAssetSummary == null
                    || existingAssetSummary.getTargetAt().compareTo(assetSummary.getTargetAt())
                            < 0) {
                assetSummaryMap.put(assetSummary.getCurrency(), assetSummary);
            }
        }

        BigDecimal previousAssetSumAsJpy = BigDecimal.ZERO;

        // 有効な(enabled=true)通貨の資産を取得
        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(TradeType.OPERATE, null, true);
        if (CollectionUtils.isEmpty(currencyConfigs)) {
            throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND);
        }
        PointUser pointUser = pointUserService.findOne(user.getId());
        BigDecimal evalProfitLossAmtTotal = BigDecimal.ZERO;
        BigDecimal incomeTotal = BigDecimal.ZERO;
        for (CurrencyConfig currencyConfig : currencyConfigs) {
            Asset asset = assetService.findOrCreate(user.getId(), currencyConfig.getCurrency());

            // 板が空の場合は0
            BigDecimal jpyConversion =
                    orderbookService.getOperateBestBidOfQuoteJpy(asset.getCurrency());
            AssetData assetData = new AssetData();
            assetData.setCurrency(asset.getCurrency());
            assetData.setJpyLockedAmount(asset.getLockedAmount().multiply(jpyConversion));
            assetData.setJpyOnhandAmount(
                    asset.getOnhandAmount()
                            .multiply(jpyConversion)
                            .setScale(SCALE_2, BigDecimal.ROUND_HALF_UP));
            assetData.setLockedAmount(asset.getLockedAmount());
            assetData.setOnhandAmount(asset.getOnhandAmount());
            assetData.setUnlockedAmount(asset.getOnhandAmount().subtract(asset.getLockedAmount()));
            assetData.setPointUser(pointUser);
            assetData.setUserId(asset.getUserId());
            // find and cal EvalProfitLossAmt
            Symbol symbolTemp =
                    symbolService.findByCondition(
                            TradeType.OPERATE,
                            CurrencyPair.valueOf(asset.getCurrency(), Currency.JPY));
            log.info("[Operate] asset symbolTemp: {}", symbolTemp);
            if (symbolTemp != null) {
                PosBestPriceData bestPrice = posBestPriceService.getBestPrice(symbolTemp.getId());
                BigDecimal nowPrice = BigDecimal.ZERO;
                if (bestPrice != null) {
                    nowPrice = bestPrice.getBestBid();
                }
                BigDecimal onhandAmount = asset.getOnhandAmount();
                ;
                BigDecimal avgAcqUnitPrice = asset.getAvgAcqUnitPrice();
                ;
                if (onhandAmount == null) {
                    onhandAmount = BigDecimal.ZERO;
                }
                if (avgAcqUnitPrice == null) {
                    avgAcqUnitPrice = nowPrice.setScale(SCALE_3, BigDecimal.ROUND_HALF_UP);
                }
                if (onhandAmount.compareTo(BigDecimal.ZERO) == 0) {
                    avgAcqUnitPrice = BigDecimal.ZERO;
                }
                BigDecimal totalAssetPrice = asset.getAssetTotal();
                if (totalAssetPrice == null) {
                    totalAssetPrice = BigDecimal.ZERO;
                }
                BigDecimal priceDifference = nowPrice.subtract(avgAcqUnitPrice);
                BigDecimal evalProfitLossAmt =
                        onhandAmount
                                .multiply(priceDifference)
                                .setScale(SCALE_2, BigDecimal.ROUND_HALF_UP);

                BigDecimal evalProfitLossAmtRate = BigDecimal.ZERO;
                if (totalAssetPrice.compareTo(BigDecimal.ZERO) != 0) {
                    evalProfitLossAmtRate =
                            evalProfitLossAmt.divide(
                                    totalAssetPrice, SCALE_4, BigDecimal.ROUND_HALF_UP);
                }
                assetData.setEvalProfitLossAmt(evalProfitLossAmt);
                assetData.setEvalProfitLossAmtRate(evalProfitLossAmtRate);
                assetData.setAvgAcqUnitPrice(
                        avgAcqUnitPrice.setScale(SCALE_3, BigDecimal.ROUND_HALF_UP));
                assetData.setNowPrice(nowPrice);
                evalProfitLossAmtTotal = evalProfitLossAmtTotal.add(evalProfitLossAmt);
                incomeTotal = incomeTotal.add(onhandAmount.multiply(avgAcqUnitPrice));
            }

            assetAllData.getAssets().add(assetData);
            assetAllData.setAssetSumAsJpy(
                    assetAllData.getAssetSumAsJpy().add(assetData.getJpyOnhandAmount()));

            AssetSummary assetSummary = assetSummaryMap.get(assetData.getCurrency());

            if (assetSummary == null) {
                continue;
            }

            previousAssetSumAsJpy =
                    previousAssetSumAsJpy.add(
                            assetSummary
                                    .getCurrentAmount()
                                    .multiply(assetSummary.getJpyConversion()));
        }

        BigDecimal assetDiffAsJpy = assetAllData.getAssetSumAsJpy().subtract(previousAssetSumAsJpy);
        assetAllData.setAssetDiffAsJpy(assetDiffAsJpy);

        if (previousAssetSumAsJpy.signum() > 0) {
            assetAllData.setAssetDiffPercent(
                    CalculatorUtil.toPercent(
                            assetDiffAsJpy, previousAssetSumAsJpy, 2, RoundingMode.HALF_UP));
        }

        // ADD evalProfitLossAmtTotal&evalProfitLossAmtRateTotal
        assetAllData.setChoiceEvalProfitLossAmtTotal(evalProfitLossAmtTotal);
        if (incomeTotal != null && incomeTotal.compareTo(BigDecimal.ZERO) != 0) {
            assetAllData.setChoiceEvalProfitLossAmtRateTotal(
                    evalProfitLossAmtTotal.divide(incomeTotal, 4, BigDecimal.ROUND_HALF_UP));
        }
        // add game power amount
        ChoicePowerUserRel userRel = choicePowerUserRelService.getChoicePowerUserRel(user.getId());
        if (userRel != null) {
            ChoicePower choicePowerResult = choicePowerService.findOne(userRel.getChoicePowerId());
            if (choicePowerResult != null) {
                assetAllData.setChoicePowerAmount(choicePowerResult.getAmount());
                log.info(
                        "[Operate] Fetched and set ChoicePower amount: {}",
                        choicePowerResult.getAmount());
            }
        }
        return ResponseEntity.ok(assetAllData);
    }

    @GetMapping("/list")
    @Operation(
            summary = "game operate get asset",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
            })
    public ResponseEntity<GlobalApiResponse<List<AssetResponseApi>>> getOperateAsset(
            @Parameter(hidden = true) @AuthenticationPrincipal UserPrincipal user)
            throws Exception {
        List<AssetResponseApi> assetResponseApiList = new ArrayList<>();
        // 有効な(enabled=true)通貨の資産を取得
        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(TradeType.OPERATE, null, true);
        if (CollectionUtils.isEmpty(currencyConfigs)) {
            throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND);
        }
        for (CurrencyConfig currencyConfig : currencyConfigs) {
            // Asset無しの場合は初期値で作成
            Asset asset = assetService.findOrCreate(user.getId(), currencyConfig.getCurrency());

            // 板が空の場合は0
            BigDecimal jpyConversion =
                    orderbookService.getOperateBestBidOfQuoteJpy(asset.getCurrency());
            AssetData assetData = getAssetData(asset, jpyConversion, null);
            AssetResponseApi assetResponseApi =
                    AssetResponseApi.builder()
                            .userId(user.getId())
                            .currency(currencyConfig.getCurrency())
                            .availableAmount(assetData.getUnlockedAmount())
                            .evalProfitLossAmt(assetData.getEvalProfitLossAmt())
                            .evalProfitLossAmtRate(assetData.getEvalProfitLossAmtRate())
                            .avgAcqUnitPrice(assetData.getAvgAcqUnitPrice())
                            .nowPrice(assetData.getNowPrice())
                            .operatePoints(assetData.getOperatePoints())
                            .build();
            assetResponseApiList.add(assetResponseApi);
        }
        process(assetResponseApiList);
        return ResponseEntity.ok(
                new GlobalApiResponse<>(HttpStatus.OK.value(), assetResponseApiList));
    }

    public static void process(List<AssetResponseApi> assetResponseApiList) {
        // 找到 currency 为 JPY 的对象
        AssetResponseApi jpyAsset =
                assetResponseApiList.stream()
                        .filter(asset -> Currency.JPY.equals(asset.getCurrency()))
                        .findFirst()
                        .orElse(null);

        if (jpyAsset == null) {
            return;
        }
        BigDecimal totalEvalProfitLossAmt = BigDecimal.ZERO;
        BigDecimal totalEvalProfitLossAmtRate = BigDecimal.ZERO;
        BigDecimal operationPoints = BigDecimal.ZERO;
        for (AssetResponseApi asset : assetResponseApiList) {
            if (!EnumSet.of(Currency.JPY, Currency.POINT).contains(asset.getCurrency())) {
                totalEvalProfitLossAmt = totalEvalProfitLossAmt.add(asset.getEvalProfitLossAmt());
                totalEvalProfitLossAmtRate =
                        totalEvalProfitLossAmtRate.add(asset.getEvalProfitLossAmtRate());
                operationPoints = operationPoints.add(asset.getOperatePoints());
            }
        }
        jpyAsset.setEvalProfitLossAmt(totalEvalProfitLossAmt);
        jpyAsset.setEvalProfitLossAmtRate(totalEvalProfitLossAmtRate);
        jpyAsset.setOperatePoints(operationPoints);
    }

    public static void processAsset(List<AssetData> assetDataList) {
        // 找到 currency 为 JPY 的对象
        AssetData jpyAsset =
                assetDataList.stream()
                        .filter(asset -> Currency.JPY.equals(asset.getCurrency()))
                        .findFirst()
                        .orElse(null);

        if (jpyAsset == null) {
            return;
        }
        BigDecimal totalEvalProfitLossAmt = BigDecimal.ZERO;
        BigDecimal totalEvalProfitLossAmtRate = BigDecimal.ZERO;
        BigDecimal operationPoints = BigDecimal.ZERO;
        BigDecimal totalAssetPrice = BigDecimal.ZERO;
        for (AssetData asset : assetDataList) {
            if (!EnumSet.of(Currency.JPY, Currency.POINT).contains(asset.getCurrency())) {
                totalEvalProfitLossAmt = totalEvalProfitLossAmt.add(asset.getEvalProfitLossAmt());
                totalAssetPrice = totalAssetPrice.add(asset.getAssetTotal());
                operationPoints = operationPoints.add(asset.getOperatePoints());
            }
        }
        if (totalAssetPrice.compareTo(BigDecimal.ZERO) != 0) {
            totalEvalProfitLossAmtRate =
                    totalEvalProfitLossAmt.divide(totalAssetPrice, SCALE_4, RoundingMode.HALF_UP);
        }
        jpyAsset.setEvalProfitLossAmt(totalEvalProfitLossAmt);
        jpyAsset.setEvalProfitLossAmtRate(totalEvalProfitLossAmtRate);
        jpyAsset.setOperatePoints(operationPoints);
    }
}

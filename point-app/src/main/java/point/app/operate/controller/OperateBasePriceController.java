package point.app.operate.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.CurrencyPair;
import point.common.constant.OrderSide;
import point.common.entity.Symbol;
import point.common.service.SymbolService;
import point.common.util.JsonUtil;
import point.pos.model.PosBestPriceData;
import point.pos.service.PosBestPriceService;

@Timed
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/operate")
public class OperateBasePriceController {

    private final PosBestPriceService posBestPriceService;
    private final SymbolService symbolService;
    private final int DEFAULT_QUOTE_PRECISION = 18;

    @GetMapping("/board-price")
    public ResponseEntity<ArrayList<String>> getBoardPrice(
            @RequestParam("symbolId") Long symbolId,
            @RequestParam("orderSide") OrderSide orderSide,
            @RequestParam("orderAmount") BigDecimal orderAmount) {
        ArrayList<String> priceList = new ArrayList<>();

        Symbol symbol = symbolService.findOne(symbolId);

        if (symbol == null || orderSide == null || orderAmount == null) {
            priceList.add(null);
            priceList.add(null);
            return ResponseEntity.ok(priceList);
        }
        PosBestPriceData bestPrice = posBestPriceService.getBestPrice(symbolId);
        CurrencyPair currencyPair = symbol.getCurrencyPair();
        if (Objects.isNull(bestPrice)) {
            priceList.add(null);
            return ResponseEntity.ok(priceList);
        } else {
            if (orderSide == OrderSide.SELL) {
                BigDecimal mmBidPrice =
                        bestPrice
                                .getBestMmBid()
                                .setScale(DEFAULT_QUOTE_PRECISION, RoundingMode.DOWN);
                BigDecimal bidPrice = bestPrice.getBestBid();
                priceList.add(mmBidPrice.toPlainString());
                priceList.add(bidPrice.toPlainString());
                priceList.add(String.valueOf(BigDecimal.ZERO));
            } else {
                BigDecimal amount =
                        orderAmount.divide(
                                bestPrice.getBestAsk(), DEFAULT_QUOTE_PRECISION, RoundingMode.UP);
                BigDecimal mmAskPrice =
                        bestPrice.getBestMmAsk().setScale(DEFAULT_QUOTE_PRECISION, RoundingMode.UP);
                BigDecimal askPrice = bestPrice.getBestAsk();
                priceList.add(mmAskPrice.toPlainString());
                priceList.add(askPrice.toPlainString());
                priceList.add(amount.toPlainString());
            }
        }
        log.info("bestPrice: {}", JsonUtil.encode(bestPrice));
        return ResponseEntity.ok(priceList);
    }

    @Operation(
            summary = "get operate price",
            security = @SecurityRequirement(name = "x-auth"),
            responses = {
                @ApiResponse(responseCode = "200", description = "Success"),
                @ApiResponse(responseCode = "400", description = "Bad Request")
            })
    @GetMapping("/price")
    public ResponseEntity<List<PosBestPriceData>> getBsePriceAll(
            @RequestParam("symbolId") Long[] symbolIds) {
        List<PosBestPriceData> list = new ArrayList<>();
        for (Long symbolId : symbolIds) {
            PosBestPriceData bestPrice = posBestPriceService.getBestPrice(symbolId);
            if (bestPrice != null) {
                list.add(bestPrice);
            }
        }
        return ResponseEntity.ok(list);
    }
}

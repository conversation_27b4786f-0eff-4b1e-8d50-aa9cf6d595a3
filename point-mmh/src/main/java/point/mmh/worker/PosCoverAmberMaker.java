package point.mmh.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.util.JsonUtil;
import point.mmh.component.Worker;
import point.pos.service.PosCoverOrderAmberService;

@Slf4j
@Component
@RequiredArgsConstructor
public class PosCoverAmberMaker extends Worker {

    private final PosCoverOrderAmberService posCoverOrderAmberService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========posCoverOrderAmber start log symbol:{}==============",
                JsonUtil.encode(symbol));
        posCoverOrderAmberService.executeTrade(symbol);
        log.info("===========posCoverOrderAmber end log==============");
    }
}

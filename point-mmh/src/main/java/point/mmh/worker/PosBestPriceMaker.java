package point.mmh.worker;

import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.mmh.component.Worker;
import point.pos.service.PosBestPriceService;

@Slf4j
@RequiredArgsConstructor
@Component
public class PosBestPriceMaker extends Worker {

    private final PosBestPriceService service;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        if (Objects.isNull(symbol)) {
            log.info("The param of symbol is null, skip to handle best price");
            return;
        }
        try {
            log.info("Start to handle best price for symbol: {}", symbol);
            service.handleBestPrice(symbol);
            log.info("Finish to handle best price for symbol: {}", symbol);
        } catch (Exception e) {
            log.error(
                    "Failed to handle best price by symbol: {}, error msg: {}",
                    symbol,
                    e.getMessage(),
                    e);
        }
    }
}

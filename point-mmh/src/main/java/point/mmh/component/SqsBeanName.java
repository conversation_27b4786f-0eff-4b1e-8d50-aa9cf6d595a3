package point.mmh.component;

import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.config.SpringConfig;

/**
 * SNS トピック名、SQS キュー名を生成する。<br>
 *
 * <h3>キュー名の仕様</h3>
 *
 * <h4>Environment が local 以外 (dev, prd など) の場合</h4>
 *
 * <p>&lt;environment&gt;-&lt;トピック名&gt;
 *
 * <h4>Environment が local の場合</h4>
 *
 * <p>&lt;environment&gt;-&lt;user&gt;_&lt;トピック名&gt;
 *
 * <p>user は環境変数 USER の値で、ログイン中のユーザ名を表す。
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public final class SqsBeanName {

    private final SpringConfig springConfig;

    private String queueNamePrefix;

    @PostConstruct
    public void init() {
        queueNamePrefix = createQueueNamePrefix();

        log.debug("queueNamePrefix = " + queueNamePrefix);
    }

    private String createQueueNamePrefix() {
        StringBuilder sb = new StringBuilder();

        sb.append(springConfig.getEnvironment());
        sb.append('-');

        if (springConfig.isLocal()) {
            String user = springConfig.getUser();

            if (user != null) {
                sb.append(user);
                sb.append('_');
            }
        }

        return sb.toString();
    }

    private <W> String toBeanName(Class<W> workerClass) {
        String simpleName = workerClass.getSimpleName();
        String lowerCamelCase =
                String.valueOf(simpleName.charAt(0)).toLowerCase() + simpleName.substring(1);
        return lowerCamelCase;
    }

    public <W> String toQueueName(Class<W> workerClass) {
        String beanName = toBeanName(workerClass);
        return queueNamePrefix + beanName;
    }

    public String fromQueueName(String queueName) {
        if (!queueName.startsWith(queueNamePrefix)) {
            return null;
        }

        String beanName = queueName.substring(queueNamePrefix.length());
        return beanName;
    }
}

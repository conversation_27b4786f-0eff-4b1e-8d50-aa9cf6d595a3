package point.mmh.sqssubscriber;

import org.springframework.stereotype.Component;
import point.mmh.component.SqsSubscriber;
import point.mmh.worker.OperateCoverOrderBuyMaker;

@Component
public class OperateCoverOrderBuyMakerSqsSubscriber
        extends SqsSubscriber<OperateCoverOrderBuyMaker> {
    @Override
    public Class<OperateCoverOrderBuyMaker> getWorkerClass() {
        return OperateCoverOrderBuyMaker.class;
    }
}

#!/bin/bash

ENVIRONMENT=
BUILD_DOCKER_PUBLISH=
BUILD_DOCKER_IMAGE_VERSION_ARG=
RESTART_K8S_DEPLOYMENT=
SHOW_HELP=

# parse args
while true; do
  if [ -z $1 ]; then
    break
  fi
  case $1 in
    -h|--help) SHOW_HELP=1 ;;
    -k|--restart-deployment) RESTART_K8S_DEPLOYMENT=1 ;;
    -p|--push) BUILD_DOCKER_PUBLISH=1 ;;
    "-v") shift; BUILD_DOCKER_IMAGE_VERSION_ARG=$1 ;;
    *)
      if [ -z $ENVIRONMENT ]; then
        ENVIRONMENT=$1
      fi
      ;;
  esac
  shift
done

# validate
if [[ -z $ENVIRONMENT ]] || [[ "$SHOW_HELP" = 1 ]]; then
  cat <<EOF
usage: $0 <options> <environment>

options:
 -h or --help ... show help
 -k or --restart-deployment ... restart k8s deployment
 -p or --push ... do docker push after build
 -v <version> ... specify docker image version

example:

build image, docker push, and rolling update to dev:
./deployment/scripts/build.sh dev -p -k
EOF
  exit 1
fi

# load env file
envfile=deploy/env/$ENVIRONMENT

if [ ! -f $envfile ]; then
  echo "env file not found: $envfile"
  exit 1
fi

source $envfile

# update env
if [ ! -z $BUILD_DOCKER_IMAGE_VERSION_ARG ]; then
  BUILD_DOCKER_IMAGE_VERSION=$BUILD_DOCKER_IMAGE_VERSION_ARG
fi

# build image

ENVIRONMENT=$ENVIRONMENT \
BUILD_DOCKER_IMAGE_NAME=$BUILD_DOCKER_IMAGE_NAME \
BUILD_DOCKER_IMAGE_VERSION=$BUILD_DOCKER_IMAGE_VERSION \
gradle bootBuildImage

if [ $? != 0 ]; then
  echo "abort"
  exit 1
fi

# docker push
if [ "$BUILD_DOCKER_PUBLISH" = "1" ]; then
  URI=$BUILD_DOCKER_IMAGE_NAME:$BUILD_DOCKER_IMAGE_VERSION
  echo "docker push $URI"
  docker push $URI
fi

# restart k8s deployment
if [ "$RESTART_K8S_DEPLOYMENT" = "1" ]; then
  echo
  echo "Restart k8s deployment: $K8S_DEPLOYMENT"
  kubectl rollout restart deployment $K8S_DEPLOYMENT
fi

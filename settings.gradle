include ':point-admin'
include ':point-api'
include ':point-app'
include ':point-common'
include ':point-worker'
include 'point-mmh'

var project_name_prefix = 'bs-'

project(":point-admin").name  = project_name_prefix + "point-admin"
project(":point-api").name    = project_name_prefix + "point-api"
project(":point-app").name    = project_name_prefix + "point-app"
project(":point-common").name = project_name_prefix + "point-common"
project(":point-worker").name = project_name_prefix + "point-worker"
project(":point-mmh").name = project_name_prefix + "point-mmh"

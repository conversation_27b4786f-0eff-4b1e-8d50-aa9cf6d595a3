package point.admin.controller;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.component.CsvDownloadManager;
import point.common.constant.Currency;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.constant.ViewVariables;
import point.common.entity.AssetSummary;
import point.common.entity.PointUser;
import point.common.exception.CustomException;
import point.common.model.response.AssetSummaryReportData;
import point.common.model.response.PageData;
import point.common.service.AssetSummaryService;
import point.common.service.PointUserService;
import point.common.util.DateUnit;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/operate/asset-summary")
@Slf4j
public class V1OperateAssetSummaryRestController extends ExchangeAdminController {

    private final AssetSummaryService assetSummaryService;
    private final CsvDownloadManager<AssetSummaryReportData> downloadManager;
    private final PointUserService pointUserService;

    @GetMapping
    @PreAuthorize("@auth.check('asset-summaries')")
    public ResponseEntity<PageData<AssetSummary>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletRequest request,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws CustomException {
        PointUser user = pointUserService.findOne(userId);
        if (user == null || !UserIdType.Operate.equals(user.getUserIdentity().getIdType())) {
            return ResponseEntity.ok(new PageData<>(number, size, 0, Collections.emptyList()));
        }
        Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
        Date dateToDate = dateTo != null ? DateUnit.getTommorowStartDate(new Date(dateTo)) : null;

        // Currencyに存在するparam.currencyのみ受領する前提
        // adminは無効(enabled=false)のcurrencyでも参照可能とする
        // assets_ummary無しの場合は空リストを返す
        PageData<AssetSummary> basePageData =
                assetSummaryService.findByConditionPageData(
                        userId, currency, dateFromDate, dateToDate, number, size);
        List<AssetSummary> assetSummaryList = basePageData.getContent();
        List<AssetSummary> summaryList = new ArrayList<AssetSummary>();
        for (AssetSummary summary : assetSummaryList) {

            summary.setCurrentAmount(
                    summary.getCurrency()
                            .getScaledAmount(summary.getCurrentAmount(), RoundingMode.FLOOR));
            summary.setJpyConversion(
                    summary.getCurrency()
                            .getScaledAmount(summary.getJpyConversion(), RoundingMode.FLOOR));
            summaryList.add(summary);
        }
        PageData<AssetSummary> pageData =
                new PageData<AssetSummary>(
                        basePageData.getNumber(),
                        basePageData.getSize(),
                        basePageData.getTotalElements(),
                        summaryList);

        if (pageData.getTotalElements() > 50000) {
            throw new CustomException(ErrorCode.SEARCH_OVER_FIFTY_THOUSAND);
        }
        return ResponseEntity.ok(pageData);
    }

    @GetMapping("/download/check")
    public ResponseEntity<Long> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        PointUser user = pointUserService.findOne(userId);
        if (user == null || !UserIdType.Operate.equals(user.getUserIdentity().getIdType())) {
            return ResponseEntity.ok(0L);
        }
        Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
        Date dateToDate = dateTo != null ? DateUnit.getTommorowStartDate(new Date(dateTo)) : null;

        Long count =
                assetSummaryService.countByCondition(userId, currency, dateFromDate, dateToDate);
        if (count > 50000) {
            throw new CustomException(ErrorCode.DOWNLOAD_OVER_FIFTY_THOUSAND);
        }
        return ResponseEntity.ok(count);
    }

    @GetMapping("/download")
    public void download(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "currency", required = false) Currency currency,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        PointUser user = pointUserService.findOne(userId);
        if (user == null || !UserIdType.Operate.equals(user.getUserIdentity().getIdType())) {
            return;
        }
        Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
        Date dateToDate = dateTo != null ? DateUnit.getTommorowStartDate(new Date(dateTo)) : null;

        PageData<AssetSummary> assetSummaryPageData =
                assetSummaryService.findByConditionPageData(
                        userId, currency, dateFromDate, dateToDate, 0, Integer.MAX_VALUE);

        List<PointUser> userList = new ArrayList<>();
        if (userId == null) {
            userList =
                    pointUserService.findAll().stream()
                            .filter(
                                    pointUser ->
                                            UserIdType.Operate.equals(
                                                    pointUser.getUserIdentity().getIdType()))
                            .toList();
        }

        List<AssetSummaryReportData> reportDataList = new ArrayList<>();
        for (AssetSummary assetSummary : assetSummaryPageData.getContent()) {
            if (!user.getId().equals(assetSummary.getUserId())) {
                List<PointUser> userListFind =
                        userList.stream()
                                .filter(u -> u.getId().equals(assetSummary.getUserId()))
                                .toList();
                if (CollectionUtils.isEmpty(userListFind)) {
                    user = new PointUser();
                } else {
                    user = userListFind.get(0);
                }
            }
            reportDataList.add(new AssetSummaryReportData().setProperties(assetSummary, user));
        }
        log.info("****************operate assetSummaryLog Download Start********************");
        String fileNamePrefix = "operate_asset_summary";
        downloadManager.download(
                response, reportDataList, fileNamePrefix, AssetSummaryReportData.getReportHeader());
        log.info("****************operate assetSummaryLog Download End********************");
    }
}

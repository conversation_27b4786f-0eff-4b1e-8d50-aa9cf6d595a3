package point.admin.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportTradesStatement;
import point.common.component.CsvDownloadManager;
import point.common.constant.ErrorCode;
import point.common.entity.Symbol;
import point.common.entity.User;
import point.common.exception.CustomException;
import point.common.service.SymbolService;
import point.common.service.SystemConfigService;
import point.common.service.UserService;
import point.common.util.DateUnit;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report-trades")
public class V1ReportTradesController {
    private final SymbolService symbolService;
    private final UserService userService;
    private final SystemConfigService systemConfigService;
    private final PosTradeService posTradeService;
    private final CsvDownloadManager<ReportTradesStatement> downloadManager;

    @GetMapping()
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "symbolId", required = false) Long symbolId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        if (symbolId == null) {
            log.error("reportTradesDownload: currency pair is null");
            throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        }
        Symbol posSymbol = symbolService.findOne(symbolId);
        if (posSymbol == null) {
            return;
        }

        Date dateFromDate = (dateFrom == null) ? null : new Date(dateFrom);
        Date dateToDate = (dateTo == null) ? null : DateUnit.getTommorowStartDate(new Date(dateTo));

        // 自社口座を全て取得する
        final var systemUsers =
                userService.findByInsideAccountFlg(true).stream()
                        .filter(user -> user.getUserInfo() != null)
                        .toList();
        String ownerIds[] =
                systemUsers.stream()
                        .map(
                                (owner) -> {
                                    return owner.getId().toString();
                                })
                        .collect(Collectors.joining(","))
                        .split(",");

        /*
         * ①販売所
         */
        List<ReportTradesStatement> posReportDataList = new ArrayList<ReportTradesStatement>();

        /*
         * ②販売所
         */
        List<ReportTradesStatement> reportDataList = new ArrayList<ReportTradesStatement>();

        // テーブル「pos_trade」からデータを取得する
        List<PosTrade> posTradesFromSymbol =
                posTradeService.findAllByCondition(
                        posSymbol.getId(),
                        null,
                        null,
                        null,
                        null,
                        dateFrom,
                        dateToDate.getTime(),
                        null,
                        null,
                        null);

        for (PosTrade posTrade : posTradesFromSymbol) {
            User user = userService.findOne(posTrade.getUserId());
            User targetUser = new User();
            reportDataList.add(
                    new ReportTradesStatement()
                            .setProperties(
                                    posTrade,
                                    posSymbol.getCurrencyPair(),
                                    user,
                                    targetUser,
                                    ownerIds,
                                    posSymbol));
        }

        List<PosTrade> posTradesFromHistory =
                posTradeService.findFromPosTradeHistory(
                        posSymbol,
                        null,
                        null,
                        null,
                        null,
                        dateFromDate,
                        dateToDate,
                        0,
                        Integer.MAX_VALUE);

        for (PosTrade posTradeHistory : posTradesFromHistory) {
            User user = userService.findOne(posTradeHistory.getUserId());
            User targetUser = new User();
            reportDataList.add(
                    new ReportTradesStatement()
                            .setProperties(
                                    posTradeHistory,
                                    posSymbol.getCurrencyPair(),
                                    user,
                                    targetUser,
                                    ownerIds,
                                    posSymbol));
        }

        reportDataList.sort((x, y) -> x.getTradeDate().compareTo(y.getTradeDate()));
        reportDataList.addAll(posReportDataList);

        try {
            downloadManager.download(
                    response,
                    reportDataList,
                    "Report_Trades_"
                            + new DateTime(dateFrom)
                                    .toDateTime(DateTimeZone.forID("Asia/Tokyo"))
                                    .toString("yyyyMMdd_")
                            + new DateTime(dateTo)
                                    .toDateTime(DateTimeZone.forID("Asia/Tokyo"))
                                    .toString("yyyyMMdd"),
                    true);
        } catch (CustomException e) {
            log.warn(e.getClass().getName() + ": " + e.getMessage(), e);
        }
    }
}

package point.admin.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.component.CsvDownloadManager;
import point.common.constant.ViewVariables;
import point.common.entity.HighValueTrader;
import point.common.exception.CustomException;
import point.common.model.response.HighValueTraderReportData;
import point.common.model.response.PageData;
import point.common.service.HighValueTraderService;
import point.common.util.DateUnit;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/high-value-trader")
public class V1HighValueTraderRestController extends ExchangeAdminController {

    private final HighValueTraderService highValueTraderService;
    private final CsvDownloadManager<HighValueTraderReportData> downloadManager;

    @GetMapping
    @PreAuthorize("@auth.check('high-value-trader')")
    public ResponseEntity<PageData<HighValueTrader>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(
                            value = "number",
                            required = false,
                            defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(
                            value = "size",
                            required = false,
                            defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws CustomException {

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
        return ResponseEntity.ok(
                highValueTraderService.findByCondition(userId, dateFrom, dateTo, number, size));
    }

    @GetMapping("/download")
    public void download(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        List<HighValueTraderReportData> reportDataList = new ArrayList<HighValueTraderReportData>();
        PageData<HighValueTrader> pageData =
                highValueTraderService.findByCondition(
                        userId, dateFrom, dateTo, 0, Integer.MAX_VALUE);
        for (HighValueTrader highValueTrader : pageData.getContent()) {
            reportDataList.add(new HighValueTraderReportData().setProperties(highValueTrader));
        }

        String fileNamePrefix = "highValueTrader";
        downloadManager.download(
                response,
                reportDataList,
                fileNamePrefix,
                HighValueTraderReportData.getReportHeader());
    }
}

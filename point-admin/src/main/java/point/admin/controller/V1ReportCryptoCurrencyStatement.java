package point.admin.controller;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import net.logstash.logback.encoder.org.apache.commons.lang3.ObjectUtils;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportCryptoCurrencyStatement;
import point.common.component.CsvDownloadManager;
import point.common.constant.*;
import point.common.entity.*;
import point.common.service.*;
import point.common.util.DateUnit;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

// 暗号資産管理明細簿
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report_crypto_currency_statement")
public class V1ReportCryptoCurrencyStatement {

    private final SymbolService symbolService;
    private final AssetSummaryService assetSummaryService;
    private final UserService userService;
    private final CsvDownloadManager<ReportCryptoCurrencyStatement> downloadManager;
    private final PosTradeService posTradeService;

    // 管理画面からの検索で呼び出される
    // adminUser: 操作管理者
    // date：対象日時(ex 1675350000000)
    @GetMapping()
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "date", required = false) Long paramDate)
            throws Exception {

        // パラメータ日時がなければ現在時刻を基本日時とする
        final var date = paramDate == null ? System.currentTimeMillis() : paramDate;

        // 日時からfromToを作成する
        final var fromTo = DateUnit.createFromTo(date);

        // DBからデータを取得する
        final var readDto = read(fromTo.getLeft(), fromTo.getRight());

        // 出力DTOを生成する
        final var writeDtos = process(readDto);

        // ダウンロード情報を返却する
        downloadManager.download(response, writeDtos, "report_crypto_currency_statement", true);
    }

    // DBからデータを取得する
    protected ReadData read(Date dateFrom, Date dateTo) {
        // Symbolを全て取得する
        final var symbols = symbolService.findAll();
        // 自社口座を全て取得する
        final var systemUsers =
                userService.findByInsideAccountFlg(true).stream()
                        .filter(user -> user.getUserInfo() != null)
                        .toList();
        String ownerIds[] =
                systemUsers.stream()
                        .map(
                                (owner) -> {
                                    return owner.getId().toString();
                                })
                        .collect(Collectors.joining(","))
                        .split(",");
        // ユーザを全て取得する
        final var users =
                userService.findAll().stream().filter(user -> user.getUserInfo() != null).toList();
        // 日付から資産集計を取得する
        final var assetSummaries =
                assetSummaryService.findByCondition(
                        null, null, DateUnit.toYesterday(dateFrom), DateUnit.toYesterday(dateTo));
        List<Symbol> posSymbols =
                symbols.stream().filter(x -> x.getTradeType() == TradeType.INVEST).toList();
        List<PosTrade> posTrades = new ArrayList<>();
        posSymbols.forEach(
                symbol -> {
                    // テーブル「pos_trade」からデータを取得する
                    List<PosTrade> posTradesFromSymbol =
                            posTradeService
                                    .findAllByCondition(
                                            symbol.getId(),
                                            null,
                                            null,
                                            null,
                                            null,
                                            dateFrom.getTime(),
                                            dateTo.getTime(),
                                            null,
                                            null,
                                            null)
                                    .stream()
                                    .filter(
                                            posTrade ->
                                                    !Arrays.asList(ownerIds)
                                                            .contains(
                                                                    posTrade.getUserId()
                                                                            .toString()))
                                    .toList();
                    if (posTrades != null) {
                        posTrades.addAll(posTradesFromSymbol);
                    }

                    // 販売所約定履歴Historyを取得
                    List<PosTrade> posTradesFromHistory =
                            posTradeService
                                    .findFromPosTradeHistory(
                                            symbol,
                                            null,
                                            null,
                                            null,
                                            null,
                                            dateFrom,
                                            dateTo,
                                            0,
                                            Integer.MAX_VALUE)
                                    .stream()
                                    .filter(
                                            posTrade ->
                                                    !Arrays.asList(ownerIds)
                                                            .contains(
                                                                    posTrade.getUserId()
                                                                            .toString()))
                                    .toList();
                    if (posTrades != null) {
                        posTrades.addAll(posTradesFromHistory);
                    }
                });

        // 取得データを生成する
        return new ReadData(symbols, users, assetSummaries, posTrades);
    }

    // 取得データを出力形式に整形する
    protected List<ReportCryptoCurrencyStatement> process(ReadData readData) {
        // symbolをMAP化する
        final var symbolMap =
                readData.symbols.stream()
                        .collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // ユーザをMAP化する
        final var userMap =
                readData.users.stream().collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // 約定をWrapする(販売所)
        final var posTradeWrappers =
                readData.posTrades.stream()
                        .map(
                                it ->
                                        new ReportDtoWrapper(
                                                symbolMap, userMap.get(it.getUserId()), it));

        // WrapされたアイテムをMAP化する
        final var wrapperMap =
                Stream.of(posTradeWrappers)
                        .flatMap(Function.identity())
                        .collect(Collectors.groupingBy(ReportDtoWrapper::getAssetKey));
        // 資産合計をMAP化する
        final var assetSummaryMap =
                readData.assetSummaries.stream()
                        .collect(Collectors.toMap(AssetSummary::getAssetKey, it -> it));
        wrapperMap.entrySet().stream()
                .forEach(
                        e -> {
                            if (ObjectUtils.isEmpty(assetSummaryMap.get(e.getKey()))) {
                                AssetSummary assetSummary = new AssetSummary();
                                assetSummary.setCurrentAmount(BigDecimal.ZERO);
                                assetSummaryMap.put(e.getKey(), assetSummary);
                            }
                        });

        // Wrapされたアイテムを資産毎に出力DTOに変換する
        return assetSummaryMap.entrySet().stream()
                .flatMap(
                        e -> {
                            final var wrappers =
                                    wrapperMap.getOrDefault(e.getKey(), new ArrayList<>()).stream()
                                            .sorted(Comparator.comparing(ReportDtoWrapper::getDate))
                                            .toList();
                            var balance = e.getValue().getCurrentAmount();
                            final var writeDtos = new ArrayList<ReportCryptoCurrencyStatement>();
                            for (var wrapper : wrappers) {
                                final var writeDto = wrapper.toWriteDto(balance);
                                balance = writeDto.balanceBigDecimal();
                                writeDtos.add(writeDto);
                            }
                            return writeDtos.stream();
                        })
                .sorted(
                        Comparator.comparing(ReportCryptoCurrencyStatement::userIdLong) // ユーザIDの昇順
                                .thenComparing(ReportCryptoCurrencyStatement::date) // 作成日時の昇順
                                .thenComparing(ReportCryptoCurrencyStatement::currency) // 通貨名
                        )
                .toList();
    }

    public record ReadData(
            List<Symbol> symbols,
            List<User> users,
            List<AssetSummary> assetSummaries,
            List<PosTrade> posTrades) {}

    // どれか1つだけ保持している
    public record ReportDtoWrapper(Map<Long, Symbol> symbolMap, User user, PosTrade posTrade) {

        public AssetKey getAssetKey() {
            if (posTrade != null) {
                final var currencyPair = symbolMap.get(posTrade.getSymbolId()).getCurrencyPair();
                return new AssetKey(posTrade.getUserId(), currencyPair.getBaseCurrency());
            }
            throw new RuntimeException("assetKey not found");
        }

        private Date getDate() {
            if (posTrade != null) {
                return posTrade.getCreatedAt();
            }
            throw new RuntimeException("date not found");
        }

        private ReportCryptoCurrencyStatement toWriteDto(BigDecimal balance) {
            if (posTrade != null) {
                return ReportCryptoCurrencyStatement.create(symbolMap, user, posTrade, balance);
            }
            throw new RuntimeException("writeDto not found");
        }
    }
}

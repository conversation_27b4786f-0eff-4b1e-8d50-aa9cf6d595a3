package point.admin.controller;

import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import point.admin.entity.AdminUser;
import point.admin.model.request.CoverOrderConfigUpdateForm;
import point.common.constant.*;
import point.common.entity.CoverOrderConfig;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.model.response.CoverOrderConfigData;
import point.common.model.response.PageData;
import point.common.service.CoverOrderConfigService;
import point.common.service.SymbolService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/operate/cover-order-config")
public class V1CoverOrderConfigOperateRestController extends ExchangeAdminController {
    private final CoverOrderConfigService coverOrderConfigService;
    private final SymbolService symbolService;

    // adminでは無効な通貨ペア(currencyPairConfig.enabled=false)も処理対象とするため
    // paramのsymbolId,currencyPairの有効性チェック不要

    @GetMapping
    public ResponseEntity<CoverOrderConfigData> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = true) Long id)
            throws CustomException {

        CoverOrderConfig coverOrderConfig = coverOrderConfigService.findOne(id);
        if (coverOrderConfig == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_COVER_ORDER_CONFIG_NOT_FOUND);
        }

        Symbol symbol = symbolService.findOne(coverOrderConfig.getSymbolId());
        if (symbol == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        return ResponseEntity.ok(
                new CoverOrderConfigData()
                        .setProperties(symbol.getCurrencyPair(), coverOrderConfig));
    }

    @GetMapping("/page")
    @PreAuthorize("@auth.check('operate-cover-order-config')")
    public ResponseEntity<PageData<CoverOrderConfigData>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "currencyPair", required = false) CurrencyPair currencyPair,
            @RequestParam(value = "exchange", required = false) Exchange exchange,
            @RequestParam(value = "enabled", required = false) Boolean enabled,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws CustomException {
        PageData<CoverOrderConfig> data = new PageData<CoverOrderConfig>(number, size, 0, null);
        PageData<CoverOrderConfigData> pg =
                new PageData<CoverOrderConfigData>(number, size, 0, null);
        PageData<CoverOrderConfigData> temp =
                new PageData<CoverOrderConfigData>(number, size, 0, null);

        if (currencyPair != null) {
            Symbol symbol = symbolService.findByCondition(TradeType.OPERATE, currencyPair);
            if (symbol == null) {
                return ResponseEntity.ok(pg);
            }
            data =
                    coverOrderConfigService.findByConditionPageData(
                            symbol.getId(), exchange, enabled, number, size, TradeType.OPERATE);
        } else {
            if (CollectionUtils.isEmpty(
                    symbolService.findAllListByCondition(TradeType.OPERATE, null))) {
                return ResponseEntity.ok(pg);
            }
            data =
                    coverOrderConfigService.findByConditionPageData(
                            null, exchange, enabled, number, size, TradeType.OPERATE);
        }

        temp.addTotalElements(data.getTotalElements());

        for (CoverOrderConfig coverOrderConfig : data.getContent()) {
            Symbol symbol = symbolService.findOne(coverOrderConfig.getSymbolId());
            if (symbol == null) {
                return ResponseEntity.ok(pg);
            }
            temp.getContent()
                    .add(
                            new CoverOrderConfigData()
                                    .setProperties(symbol.getCurrencyPair(), coverOrderConfig));
        }
        pg =
                new PageData<CoverOrderConfigData>(
                        number, size, temp.getTotalElements(), temp.getContent());

        return ResponseEntity.ok(pg);
    }

    @PutMapping
    public ResponseEntity<CoverOrderConfigData> update(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody CoverOrderConfigUpdateForm form)
            throws CustomException {

        CoverOrderConfig coverOrderConfig = coverOrderConfigService.findOne(form.getId());

        if (coverOrderConfig == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        Symbol symbol = symbolService.findOne(coverOrderConfig.getSymbolId());
        if (symbol == null) {
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
        if (form.getRangePricePercent() != null) {
            coverOrderConfig.setRangePricePercent(form.getRangePricePercent());
        }
        coverOrderConfig.setMinOrderAmount(form.getMinOrderAmount());
        coverOrderConfig.setEnabled(form.isEnabled());
        if (form.getTradeType() != null) {
            coverOrderConfig.setTradeType(form.getTradeType());
        }
        return ResponseEntity.ok(
                new CoverOrderConfigData()
                        .setProperties(
                                symbol.getCurrencyPair(),
                                coverOrderConfigService.save(coverOrderConfig)));
    }

    @DeleteMapping
    public ResponseEntity<CoverOrderConfig> delete(
            @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
            throws CustomException {

        CoverOrderConfig coverOrderConfig = coverOrderConfigService.findOne(id);

        if (coverOrderConfig == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_COVER_ORDER_CONFIG_NOT_FOUND);
        }

        coverOrderConfigService.delete(coverOrderConfig);
        return ResponseEntity.ok(coverOrderConfig);
    }
}

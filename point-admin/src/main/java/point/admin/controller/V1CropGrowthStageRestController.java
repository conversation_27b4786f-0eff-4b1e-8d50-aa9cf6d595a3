package point.admin.controller;

import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import point.common.constant.ViewVariables;
import point.common.entity.CropGrowthStage;
import point.common.entity.CropGrowthStageHistory;
import point.common.model.request.CropGrowthStageUpdateRequest;
import point.common.model.response.CropGrowthStageListData;
import point.common.model.response.PageData;
import point.common.service.CropGrowthStageHistoryService;
import point.common.service.CropGrowthStageService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/crop-growth-stage")
public class V1CropGrowthStageRestController extends ExchangeAdminController {

    private final CropGrowthStageService cropGrowthStageService;
    private final CropGrowthStageHistoryService cropGrowthStageHistoryService;

    @GetMapping("/select")
    @PreAuthorize("@auth.check('crop-growth-stage-list')")
    public ResponseEntity<List<CropGrowthStage>> get(
            @RequestParam(value = "tradeType", required = false, defaultValue = StringUtils.EMPTY)
                    String tradeType)
            throws Exception {

        if (StringUtils.isBlank(tradeType)) {
            return ResponseEntity.ok(cropGrowthStageService.findAll());
        }
        return ResponseEntity.ok(
                cropGrowthStageService.findAll().stream()
                        .filter(
                                d ->
                                        StringUtils.equalsIgnoreCase(
                                                d.getTradeType().getName(), tradeType))
                        .collect(Collectors.toList()));
    }

    @GetMapping("/select-data-list")
    @PreAuthorize("@auth.check('crop-growth-stage-list')")
    public ResponseEntity<CropGrowthStageListData> getListData(
            @RequestParam(value = "tradeType", required = false, defaultValue = StringUtils.EMPTY)
                    String tradeType)
            throws Exception {
        return ResponseEntity.ok(cropGrowthStageService.selectCropGrowthStageList(tradeType));
    }

    @PostMapping("/update")
    @PreAuthorize("@auth.check('crop-growth-stage-list')")
    public ResponseEntity<CropGrowthStage> update(@RequestBody CropGrowthStageUpdateRequest request)
            throws Exception {

        cropGrowthStageService.update(request);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/select-history-page")
    public ResponseEntity<PageData<CropGrowthStageHistory>> getHistory(
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size,
            @RequestParam(value = "tradeType", required = false, defaultValue = StringUtils.EMPTY)
                    String tradeType) {
        return ResponseEntity.ok(
                cropGrowthStageHistoryService.findByConditionPageData(tradeType, number, size));
    }
}

package point.admin.controller;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportCandlestickStatement;
import point.common.component.CsvDownloadManager;
import point.common.component.DataSourceManager;
import point.common.constant.CandlestickType;
import point.common.constant.PosConstants;
import point.common.constant.TradeType;
import point.common.entity.Symbol;
import point.common.service.SymbolService;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report-candlestick")
public class V1ReportCandlestickController {
    private final SymbolService symbolService;
    private final DataSourceManager dataSourceManager;
    private final CsvDownloadManager<ReportCandlestickStatement> downloadManager;

    @GetMapping()
    @PreAuthorize("@auth.check('report-candlestick')")
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "symbolId", required = true) Long symbolId,
            @RequestParam(value = "candlestickType", required = true) String candlestickTypeString,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        Symbol posSymbol = symbolService.findOne(symbolId);
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        /*
         * ①販売所
         */
        List<ReportCandlestickStatement> posStatements = new ArrayList<>();
        if (posSymbol != null) {
            CandlestickType candlestickType = CandlestickType.valueOfName(candlestickTypeString);
            String candlestickTableName = "pos_candlestick";
            String sqlTemplate =
                    """
                SELECT
                    c.id as id,
                    c.symbol_id as symbol_id,
                    c.candlestick_type as candlestick_type,
                    c.target_at as target_at,
                    c.`open` as `open`,
                    c.high as high,
                    c.low as low,
                    c.`close` as `close`,
                    c.volume as volume,
                    c.fixed as fixed,
                    c.created_at as created_at,
                    c.updated_at as updated_at
                FROM
                    %s c
                WHERE
                    c.candlestick_type = '%s'
                    and c.created_at >= :from
                    and c.created_at <= :to
                    and c.symbol_id = :symbolId
            """;
            String sql =
                    String.format(sqlTemplate, candlestickTableName, candlestickType.getName());

            try {
                Query query = entityManager.createNativeQuery(sql);

                query.setParameter("from", dateFrom == null ? null : new Date(dateFrom));
                query.setParameter(
                        "to",
                        dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo)));
                query.setParameter("symbolId", posSymbol == null ? null : posSymbol.getId());

                @SuppressWarnings("unchecked")
                List<Object[]> list = query.getResultList();

                for (int i = 0; i < list.size(); i++) {
                    ReportCandlestickStatement statement = new ReportCandlestickStatement();
                    Object[] rec = list.get(i);
                    statement.setId(String.valueOf(rec[0]));
                    statement.setSymbolId(String.valueOf(rec[1]));
                    statement.setCandlestickType(String.valueOf(rec[2]));
                    statement.setTargetAt(
                            FormatUtil.formatJst(
                                    FormatUtil.parse(
                                            String.valueOf(rec[3]),
                                            FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS_S),
                                    FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS));
                    statement.setOpen(
                            toStrigForReportNotByCurrency(
                                    new BigDecimal(String.valueOf(rec[4])).stripTrailingZeros(),
                                    numberFormat));
                    statement.setHigh(
                            toStrigForReportNotByCurrency(
                                    new BigDecimal(String.valueOf(rec[5])).stripTrailingZeros(),
                                    numberFormat));
                    statement.setLow(
                            toStrigForReportNotByCurrency(
                                    new BigDecimal(String.valueOf(rec[6])).stripTrailingZeros(),
                                    numberFormat));
                    statement.setClose(
                            toStrigForReportNotByCurrency(
                                    new BigDecimal(String.valueOf(rec[7])).stripTrailingZeros(),
                                    numberFormat));
                    statement.setVolume(
                            toStrigForReportNotByCurrency(
                                    new BigDecimal(String.valueOf(rec[8])).stripTrailingZeros(),
                                    numberFormat));
                    statement.setFixed(String.valueOf(rec[9]));
                    statement.setCreatedAt(
                            FormatUtil.formatJst(
                                    FormatUtil.parse(
                                            String.valueOf(rec[10]),
                                            FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS_S),
                                    FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS));
                    statement.setUpdatedAt(
                            FormatUtil.formatJst(
                                    FormatUtil.parse(
                                            String.valueOf(rec[11]),
                                            FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS_S),
                                    FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS));
                    statement.setTradeType(
                            TradeType.INVEST.equals(posSymbol.getTradeType())
                                    ? PosConstants.INVEST_TRADE_TYPE_NAME
                                    : PosConstants.OPERATE_TRADE_TYPE_NAME);

                    posStatements.add(statement);
                }
            } finally {
                entityManager.clear();
            }
        }

        downloadManager.download(response, posStatements, "report_candlestick", true);
    }

    private static String toStrigForReportNotByCurrency(
            BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue = value.toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}

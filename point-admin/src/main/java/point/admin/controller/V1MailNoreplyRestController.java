package point.admin.controller;

import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.ErrorCode;
import point.common.constant.ViewVariables;
import point.common.entity.MailNoreply;
import point.common.exception.CustomException;
import point.common.model.request.MailNoreplyPutForm;
import point.common.model.response.PageData;
import point.common.service.MailNoreplyService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/mail/noreply")
public class V1MailNoreplyRestController extends ExchangeAdminController {

    private final MailNoreplyService mailNoreplyService;

    @GetMapping
    @PreAuthorize("@auth.check('mail-noreply')")
    public ResponseEntity<PageData<MailNoreply>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        return ResponseEntity.ok(
                mailNoreplyService.findByConditionPageData(dateFrom, dateTo, number, size));
    }

    @GetMapping("/{id}")
    public ResponseEntity<MailNoreply> get(
            @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id) throws Exception {

        return ResponseEntity.ok(mailNoreplyService.findOne(id));
    }

    @PutMapping
    public ResponseEntity<MailNoreply> put(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody MailNoreplyPutForm form)
            throws Exception {

        MailNoreply mailNoreply = mailNoreplyService.findOne(form.getId());
        if (mailNoreply == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_MAIL_NOREPLY_NOT_FOUND);
        }

        mailNoreply.setFromAddress(form.getFromAddress());
        mailNoreply.setTitle(form.getTitle());
        mailNoreply.setContents(form.getContents());

        return ResponseEntity.ok(mailNoreplyService.save(mailNoreply));
    }
}

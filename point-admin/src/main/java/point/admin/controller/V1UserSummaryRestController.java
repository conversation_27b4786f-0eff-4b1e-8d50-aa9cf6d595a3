package point.admin.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportUserSummary;
import point.common.component.CsvDownloadManager;
import point.common.constant.ViewVariables;
import point.common.entity.UserSummary;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.service.UserSummaryService;
import point.common.util.DateUnit;

@RequestMapping("/admin/v1/user-summary")
@RequiredArgsConstructor
@RestController
public class V1UserSummaryRestController extends ExchangeAdminController {

    private final UserSummaryService userSummaryService;
    private final CsvDownloadManager<ReportUserSummary> downloadManager;

    @GetMapping()
    @PreAuthorize("@auth.check('user-summary')")
    public ResponseEntity<PageData<UserSummary>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws CustomException {
        Date targetAtFrom = dateFrom == null ? null : new Date(dateFrom);
        Date targetAtTo = dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo));
        PageData<UserSummary> userSummaryList;
        userSummaryList =
                userSummaryService.findByTargetAtPageData(targetAtFrom, targetAtTo, number, size);
        return ResponseEntity.ok(userSummaryList);
    }

    @GetMapping("/download")
    public void download(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        Date targetAtFrom = dateFrom == null ? null : new Date(dateFrom);
        Date targetAtTo = dateTo == null ? null : DateUnit.getTommorowStartDate(new Date(dateTo));
        PageData<UserSummary> userSummaryList;

        userSummaryList =
                userSummaryService.findByTargetAtPageData(
                        targetAtFrom, targetAtTo, 0, Integer.MAX_VALUE);

        List<ReportUserSummary> reportList =
                userSummaryList.getContent().stream()
                        .map(
                                userSummary -> {
                                    return new ReportUserSummary(userSummary);
                                })
                        .toList();
        String fileNamePrefix = "user_summary";
        downloadManager.download(response, reportList, fileNamePrefix, true);
    }
}

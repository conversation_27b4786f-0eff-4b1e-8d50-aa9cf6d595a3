package point.admin.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import point.common.model.request.IdForm;

public class AdminUserPutForm extends IdForm {

    @Getter @Setter @NotNull private String email;

    @Getter @Setter @NotNull private boolean accountNonExpired;

    @Getter @Setter @NotNull private boolean accountNonLocked;

    @Getter @Setter @NotNull private boolean credentialsNonExpired;

    @Getter @Setter @NotNull private boolean enabled;
}

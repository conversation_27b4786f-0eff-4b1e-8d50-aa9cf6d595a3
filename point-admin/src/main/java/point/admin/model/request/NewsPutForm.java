package point.admin.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import point.common.model.request.IdForm;

public class NewsPutForm extends IdForm {
    @Getter @Setter @NotNull private String NewsType;

    @Getter @Setter @NotNull private String title;

    @Getter @Setter private String contents;

    @Getter @Setter private String link;

    @Getter @Setter @NotNull private String date;

    @Getter @Setter @NotNull private boolean enabled;
}

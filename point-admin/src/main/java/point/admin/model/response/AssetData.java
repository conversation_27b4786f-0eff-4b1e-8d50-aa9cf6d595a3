package point.admin.model.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.math.RoundingMode;
import javax.persistence.*;
import lombok.Data;
import point.common.constant.Currency;
import point.common.entity.Asset;
import point.common.entity.UserIdentity;
import point.common.serializer.BigDecimalSerializer;

@Data
public class AssetData {

    private Long id;
    private Long userId;

    private Currency currency;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal onhandAmount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal lockedAmount;

    private UserIdentity userIdentity;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmt;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmtRate;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal avgAcqUnitPrice;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal assetTotal;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal operatorPoint;

    public AssetData(Asset asset) {
        this.id = asset.getId();
        this.userId = asset.getUserId();
        this.currency = asset.getCurrency();
        this.onhandAmount =
                asset.getCurrency()
                        .getOperateScaledAmount(asset.getOnhandAmount(), RoundingMode.FLOOR);
        this.lockedAmount =
                asset.getCurrency()
                        .getOperateScaledAmount(asset.getLockedAmount(), RoundingMode.FLOOR);
        this.userIdentity = asset.getUserIdentity();
        this.evalProfitLossAmt = asset.getEvalProfitLossAmt();
        this.evalProfitLossAmtRate = asset.getEvalProfitLossAmtRate();
        this.avgAcqUnitPrice = asset.getAvgAcqUnitPrice();
        this.assetTotal = asset.getAssetTotal();
    }
}

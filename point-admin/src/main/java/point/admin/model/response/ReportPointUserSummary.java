package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.operate.entity.PointUserSummary;

@Setter
@Getter
@JsonPropertyOrder({"ID", "対象日", "全ユーザー数（累計）", "作成日時", "更新日時"})
public class ReportPointUserSummary {

    @JsonProperty("ID")
    private Long id;

    @JsonProperty("対象日")
    private String targetAt = "";

    @JsonProperty("全ユーザー数（累計）")
    private Long users;

    @JsonProperty("作成日時")
    private String createdAt = "";

    @JsonProperty("更新日時")
    private String updatedAt = "";

    public ReportPointUserSummary(PointUserSummary pointUserSummary) {
        this.id = pointUserSummary.getId();
        this.targetAt =
                FormatUtil.formatJst(
                        pointUserSummary.getTargetAt(), FormatPattern.YYYY_MM_DD_SLASH);
        this.users = pointUserSummary.getUsers();
        this.createdAt =
                FormatUtil.formatJst(
                        pointUserSummary.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
        this.updatedAt =
                FormatUtil.formatJst(
                        pointUserSummary.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
    }
}

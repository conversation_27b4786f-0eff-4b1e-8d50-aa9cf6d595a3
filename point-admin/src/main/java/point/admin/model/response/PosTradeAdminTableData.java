package point.admin.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.OrderChannel;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.constant.TradeAction;
import point.common.serializer.BigDecimalSerializer;
import point.pos.entity.PosTrade;

@NoArgsConstructor
public class PosTradeAdminTableData implements Serializable {

    private static final long serialVersionUID = -3675266627847000065L;

    @Getter @Setter private String tableKey;

    @Getter @Setter private Long id;

    @Getter @Setter private Long symbolId;

    @Getter @Setter private Long userId;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderSide orderSide;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private OrderChannel orderChannel = OrderChannel.UNKNOWN;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal price;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal jpyConversion;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private TradeAction tradeAction;

    @Getter @Setter private Long orderId;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal fee;

    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Getter @Setter private String tradeType;

    @Getter @Setter private Long targetOrderId;

    @Getter @Setter private Long targetUserId;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal assetAmount;

    public PosTradeAdminTableData setProperties(PosTrade posTrade) {
        // for vue.js v-data-table
        this.setTableKey(posTrade.getSymbolId() + "_" + posTrade.getId());
        this.setId(posTrade.getId());
        this.setSymbolId(posTrade.getSymbolId());
        this.setUserId(posTrade.getUserId());
        this.setOrderSide(posTrade.getOrderSide());
        this.setOrderType(posTrade.getOrderType());
        this.setOrderChannel(posTrade.getOrderChannel());
        this.setPrice(posTrade.getPrice());
        this.setAmount(posTrade.getAmount());
        this.setJpyConversion(posTrade.getJpyConversion());
        this.setTradeAction(posTrade.getTradeAction());
        this.setOrderId(posTrade.getOrderId());
        this.setFee(posTrade.getFee());
        this.setCreatedAt(posTrade.getCreatedAt());
        this.setUpdatedAt(posTrade.getUpdatedAt());
        this.setTradeType("");
        this.setTargetOrderId(posTrade.getUserId());
        this.setTargetUserId(posTrade.getUserId());
        this.setAssetAmount(posTrade.getAssetAmount());

        return this;
    }
}

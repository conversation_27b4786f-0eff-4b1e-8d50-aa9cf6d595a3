package point.admin.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.admin.entity.AdminUserAuthority;
import point.admin.entity.AdminUserAuthority_;
import point.common.predicate.EntityPredicate;

@Component
public class AdminUserAuthorityPredicate extends EntityPredicate<AdminUserAuthority> {

    public Predicate equalAdminUserId(
            CriteriaBuilder criteriaBuilder, Root<AdminUserAuthority> root, Long adminUserId) {
        return criteriaBuilder.equal(root.get(AdminUserAuthority_.adminUserId), adminUserId);
    }

    public Predicate equalAuthority(
            CriteriaBuilder criteriaBuilder, Root<AdminUserAuthority> root, String authority) {
        return criteriaBuilder.equal(root.get(AdminUserAuthority_.authority), authority);
    }
}

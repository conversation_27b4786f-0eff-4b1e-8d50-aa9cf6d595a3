package point.admin.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.session.SessionAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.PropertyMapper;
import org.springframework.boot.web.servlet.server.Session.Cookie;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import point.common.constant.Security;

@Configuration
@ConfigurationProperties(prefix = "server.servlet.session.cookie")
public class SessionCookieConfig extends Cookie {

    @Getter @Setter private String sameSite;

    /**
     * Extended cookie initializer
     *
     * @return CookieSerializer
     * @see SessionAutoConfiguration
     */
    @Bean
    public CookieSerializer cookieSerializer() {
        DefaultCookieSerializer cookieSerializer = new DefaultCookieSerializer();

        PropertyMapper map = PropertyMapper.get().alwaysApplyingWhenNonNull();
        map.from(this::getName).to(cookieSerializer::setCookieName);
        map.from(this::getDomain).to(cookieSerializer::setDomainName);
        map.from(this::getPath).to(cookieSerializer::setCookiePath);
        map.from(this::getHttpOnly).to(cookieSerializer::setUseHttpOnlyCookie);
        map.from(this::getSecure).to(cookieSerializer::setUseSecureCookie);
        map.from(this::getMaxAge)
                .to((maxAge) -> cookieSerializer.setCookieMaxAge((int) maxAge.getSeconds()));

        // extends
        cookieSerializer.setCookieName(Security.JSESSIONID);
        cookieSerializer.setCookiePath("/");
        cookieSerializer.setSameSite(sameSite);

        return cookieSerializer;
    }
}

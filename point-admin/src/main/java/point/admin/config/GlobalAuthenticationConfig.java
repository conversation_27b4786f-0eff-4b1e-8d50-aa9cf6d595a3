package point.admin.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.GlobalAuthenticationConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import point.admin.service.AdminUserService;

@Configuration
@RequiredArgsConstructor
public class GlobalAuthenticationConfig extends GlobalAuthenticationConfigurerAdapter {

    private final AdminUserService adminUserService;

    private BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public void init(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(adminUserService).passwordEncoder(passwordEncoder);
    }

    public String getBcryptHashpw(String password, String salt) {
        return BCrypt.hashpw(password, BCrypt.gensalt());
    }

    public String getBcryptHashpw(String password) {
        return BCrypt.hashpw(password, BCrypt.gensalt());
    }

    public boolean matchesBCryptPassword(String input, String encoded) {
        return new BCryptPasswordEncoder().matches(input, encoded);
    }
}

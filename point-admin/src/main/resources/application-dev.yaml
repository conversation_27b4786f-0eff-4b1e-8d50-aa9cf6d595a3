aws:
  s3:
    kyc-bucket:
      name: kyc.bs-point-dev
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
point-admin:
  host: https://admin.dev.cxr-inc.com
  mail-address: <EMAIL>
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: admin.dev.cxr-inc.com
    environment: dev
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
coin:
  cus:
    host: https://dev.backseat-service.com
    host-external: https://dev.backseat-service.com

swagger:
  enabled: true

logging:
  level:
    root: info
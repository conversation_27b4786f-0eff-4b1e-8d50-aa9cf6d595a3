aws:
  s3:
    kyc-bucket:
      name: kyc.bs-point-stg
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
point-admin:
  host: https://admin.stg.cxr-inc.com
  mail-address: <EMAIL>
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: admin.stg.cxr-inc.com
    environment: stg
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
coin:
  cus:
    host: https://stg.backseat-service.com
    host-external: https://stg.backseat-service.com

swagger:
  enabled: true

logging:
  level:
    root: info
package point.pos.predicate;

import java.math.BigDecimal;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Exchange;
import point.common.constant.OrderSide;
import point.common.constant.OrderStatus;
import point.common.constant.OrderType;
import point.common.predicate.EntityPredicate;
import point.pos.entity.PosCoverOrder;
import point.pos.entity.PosCoverOrder_;

@Component
public class PosCoverOrderPredicate extends EntityPredicate<PosCoverOrder> {

    public Predicate equalOrderSide(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, OrderSide orderSide) {
        return criteriaBuilder.equal(root.get(PosCoverOrder_.orderSide), orderSide);
    }

    public Predicate equalOrderType(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, OrderType orderType) {
        return criteriaBuilder.equal(root.get(PosCoverOrder_.orderType), orderType);
    }

    public Predicate inOrderType(Root<PosCoverOrder> root, OrderType... orderTypes) {
        return root.get(PosCoverOrder_.orderType).in((Object[]) orderTypes);
    }

    public Predicate notInOrderType(
            CriteriaBuilder criteriaBuilder,
            Root<PosCoverOrder> root,
            OrderType... exceptOrderTypes) {
        return criteriaBuilder.not(inOrderType(root, exceptOrderTypes));
    }

    public Predicate equalExchange(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, Exchange exchange) {
        return criteriaBuilder.equal(root.get(PosCoverOrder_.exchange), exchange);
    }

    public Predicate greaterThanOrEqualToAmount(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal amount) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(PosCoverOrder_.amount), amount);
    }

    public Predicate greaterThanRemainingAmount(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal remainingAmount) {
        return criteriaBuilder.greaterThan(
                root.get(PosCoverOrder_.remainingAmount), remainingAmount);
    }

    public Predicate equalSymbol(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, Long symbolId) {
        return criteriaBuilder.equal(root.get(PosCoverOrder_.symbolId), symbolId);
    }

    public Predicate inOrderStatus(Root<PosCoverOrder> root, OrderStatus... orderStatus) {
        return root.get(PosCoverOrder_.orderStatus).in((Object[]) orderStatus);
    }

    public Predicate greaterThanOrEqualToRemainingAmount(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal remainingAmount) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(PosCoverOrder_.remainingAmount), remainingAmount);
    }

    public Predicate lessThanRemainingAmount(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal remainingAmount) {
        return criteriaBuilder.lessThanOrEqualTo(
                root.get(PosCoverOrder_.remainingAmount), remainingAmount);
    }

    public Predicate greaterThanOrEqualToNidtRemainingAmount(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal remainingAmount) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(PosCoverOrder_.remainingAmountManualNidt), remainingAmount);
    }

    public Predicate lessThanNidtRemainingAmount(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal remainingAmount) {
        return criteriaBuilder.lessThanOrEqualTo(
                root.get(PosCoverOrder_.remainingAmountManualNidt), remainingAmount);
    }

    public Predicate isUncovered(
            CriteriaBuilder criteriaBuilder, Root<PosCoverOrder> root, BigDecimal remainingAmount) {
        return criteriaBuilder.greaterThan(
                root.get(PosCoverOrder_.remainingAmountManualNidt), remainingAmount);
    }
}

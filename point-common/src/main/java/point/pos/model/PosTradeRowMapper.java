package point.pos.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;
import point.pos.entity.PosTrade;

public class PosTradeRowMapper extends AbstractPosTradeRowMapper {

    @Override
    protected Class<PosTrade> getClazz() {
        return PosTrade.class;
    }

    @Override
    public PosTrade mapRow(ResultSet rs, int rowNum) throws SQLException {
        PosTrade posTrade = super.mapRow(rs, rowNum);
        if (Objects.nonNull(posTrade)) {
            posTrade.setUserGrowthStageId(rs.getInt("user_growth_stage_id"));
        }
        return posTrade;
    }
}

package point.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.*;
import point.common.component.RedisManager.LockParams;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.model.request.PosOrderForm;
import point.common.model.response.PageData;
import point.common.service.*;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.JsonUtil;
import point.pos.entity.PosOrder;
import point.pos.entity.PosOrder_;
import point.pos.entity.PosTrade;
import point.pos.model.PosOrderRowMapper;

@Slf4j
@Service
@RequiredArgsConstructor
public class PosOrderService extends PosOrderHistoryService {

    private final CurrencyPairConfigService currencyPairConfigService;

    private final CurrencyConfigService currencyConfigService;

    private final AssetService assetService;

    private final PosTradeService posTradeService;

    private final SymbolService symbolService;

    private final MailNoreplyService mailNoreplyService;

    private final SesManager sesManager;

    private final UserService userService;

    private final RedisManager redisManager;

    private final HistoricalTransactionManager historicalTransactionManager;

    @Override
    public Class<PosOrder> getEntityClass() {
        return PosOrder.class;
    }

    @Override
    public Class<PosOrderRowMapper> getRowMapperClass() {
        return PosOrderRowMapper.class;
    }

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    public PosOrder order(Symbol symbol, User user, PosOrderForm form, OrderChannel orderChannel)
            throws Exception {
        // サーキットブレーカー検知
        if (OrderType.valueOf(form.getOrderType()) == OrderType.MARKET) {
            if (isCircuitBreaking(symbol)) {
                throw new CustomException(ErrorCode.ORDER_ERROR_CIRCUIT_BREAKER);
            }
        }
        PosOrder posOrder = this.marketOrder(symbol, user, form, orderChannel);
        if (posOrder != null) {
            checkInsiderAndRisker(posOrder);
            sendOrderMail(posOrder, mailNoreplyService, sesManager, symbol, user);
        }
        return posOrder;
    }

    private PosOrder marketOrder(
            Symbol symbol, User user, PosOrderForm form, OrderChannel orderChannel)
            throws Exception {
        log.info("ordertradelog,symbolId," + symbol.getId() + ",limitorder_start");

        CurrencyPairConfig currencyPairConfig =
                currencyPairConfigService.findByCondition(
                        symbol.getTradeType(), symbol.getCurrencyPair());

        if (!user.isTradable()) {
            log.error("Invalid user status, userId: {}", user.getId());
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER_STATUS);
        }
        if (user.getLevel() < 2) {
            log.error(
                    "Insufficient user security level, userId: {}, securityLevel {}",
                    user.getId(),
                    user.getLevel());
            throw new CustomException(ErrorCode.ORDER_ERROR_SECURITY_LEVEL);
        }

        if (currencyPairConfig == null) {
            log.error(
                    "Not found currencyPair config {}", JsonUtil.encode(symbol.getCurrencyPair()));
            throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND);
        }

        Currency baseCurrency = symbol.getCurrencyPair().getBaseCurrency();
        Currency quoteCurrency = symbol.getCurrencyPair().getQuoteCurrency();

        CurrencyConfig baseCurrencyConfig =
                currencyConfigService.findByCurrency(baseCurrency, TradeType.INVEST);
        CurrencyConfig quoteCurrencyConfig =
                currencyConfigService.findByCurrency(quoteCurrency, TradeType.INVEST);

        if (baseCurrencyConfig == null || quoteCurrencyConfig == null) {
            log.error(
                    "Not found currencyPair config,userId:  {}",
                    JsonUtil.encode(user.getId()) + ", form: " + JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND);
        }

        if (!currencyPairConfig.isTradable() || !currencyPairConfig.isEnabled()) {
            log.error(
                    "Inactive order, userId: {}, tradable: {}, enabled: {},  form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(currencyPairConfig.isTradable()),
                    JsonUtil.encode(currencyPairConfig.isEnabled()),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_ORDER_IS_INACTIVE_POS);
        }

        OrderSide orderSide = OrderSide.valueOf(form.getOrderSide());
        OrderType orderType = OrderType.valueOf(form.getOrderType());

        BigDecimal amount = form.getAmount();
        // 発注数量チェック:valid
        if (amount == null || amount.signum() < 1) {
            log.error(
                    "Order amount invalid, userId: {},  amount: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(amount),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_AMOUNT);
        }
        // 発注数量が指定桁数以内であること
        // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
        if (amount.scale() > symbol.getCurrencyPair().getPosBasePrecision()) {
            log.error(
                    "Order scale invalid, userId: {}, amount: {}, amount_scale:{}, basePrecision:{}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(amount),
                    JsonUtil.encode(amount.scale()),
                    JsonUtil.encode(symbol.getCurrencyPair().getPosBasePrecision()),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_AMOUNT);
        }
        BigDecimal price = form.getPrice();
        BigDecimal mmPrice = form.getMmPrice();
        // 発注価格チェック
        if (price == null || mmPrice == null) {
            log.error(
                    "POS order price not found, userId: {}, price: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(price),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.POS_ORDER_ERROR_PRICE_NOT_FOUND);
        }

        if (price.signum() < 1 && mmPrice.scale() < 1) {
            log.error(
                    "order price out of range, userId: {}, price: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(price),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE);
        }

        // 発注価格が指定桁数以内であること
        // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
        if (price.scale() > symbol.getCurrencyPair().getQuotePrecision()) {
            log.error(
                    "Order scale invalid, userId: {}, price: {}, price_scale:{}, quotePrecision:{}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(price),
                    JsonUtil.encode(price.scale()),
                    JsonUtil.encode(symbol.getCurrencyPair().getQuotePrecision()),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_PRICE);
        }

        // 発注数量算出（通貨ペア桁数処理後）
        BigDecimal amountScaled =
                currencyPairConfig.getCurrencyPair().getPosScaledAmount(amount, RoundingMode.FLOOR);

        if (amountScaled == null || amountScaled.signum() < 1) {
            log.error(
                    "Invalid order amount, userId: {}, amount: {}, amountScaled:{}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(amount),
                    JsonUtil.encode(amountScaled),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_AMOUNT);
        }

        // 注文価格チェック(通貨ぺア単位桁数処理後):valid
        BigDecimal priceScaled = currencyPairConfig.getCurrencyPair().getScaledPrice(price);

        // 発注数量チェック:valid
        if (priceScaled == null || priceScaled.signum() < 1) {
            log.error(
                    "Invalid order amount, userId: {}, priceScaled: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(priceScaled),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE);
        }

        // 1注文の最小・最大注文数量チェック
        // 上限撤廃フラグ(apiより)が立っているときはスキップ
        if (!user.isTradeUncapped()
                && (amountScaled.compareTo(currencyPairConfig.getMaxOrderAmount()) > 0
                        || amountScaled.compareTo(currencyPairConfig.getMinOrderAmount()) < 0)) {
            throw new CustomException(
                    ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_MINMAX,
                    "userId: "
                            + JsonUtil.encode(user.getId())
                            + ", amount: "
                            + JsonUtil.encode(amount)
                            + ", amountScaled: "
                            + JsonUtil.encode(amountScaled)
                            + ", maxOrderAmount: "
                            + JsonUtil.encode(currencyPairConfig.getMaxOrderAmount())
                            + ", minOrderAmount: "
                            + JsonUtil.encode(currencyPairConfig.getMinOrderAmount())
                            + ", form: "
                            + JsonUtil.encode(form));
        }
        // 発注総額概算算出
        // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
        BigDecimal assetAmount = priceScaled.multiply(amountScaled);

        // 発注総額チェック:valid
        if (assetAmount == null || assetAmount.signum() < 1) {
            log.error(
                    "Invalid order amount, userId: {}, assetAmount: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(assetAmount),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_AMOUNT);
        }

        BigDecimal assetAmountScaled =
                this.calculateAssetAmount(symbol, priceScaled.multiply(amountScaled));

        if (assetAmountScaled == null || assetAmountScaled.signum() < 1) {
            log.error(
                    "Invalid order amount, userId: {}, assetAmount: {}, assetAmountScaled: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(assetAmount),
                    JsonUtil.encode(assetAmountScaled),
                    JsonUtil.encode(form));
            throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_AMOUNT);
        }
        // 买 BUY jpy  卖 SELL 币
        // 資産ロック
        return (PosOrder)
                redisManager.executeWithLock(
                        getLockKey(user.getId(), baseCurrency),
                        LockParams.ORDER,
                        () -> {
                            return redisManager.executeWithLock(
                                    getLockKey(user.getId(), quoteCurrency),
                                    LockParams.ORDER,
                                    () -> {
                                        return customTransactionManager.execute(
                                                entityManager -> {
                                                    log.info(
                                                            "ordertradelog,symbolId,"
                                                                    + symbol.getId()
                                                                    + ",marketorder_transaction_start");

                                                    // 【注意】トランザクション内ではentityManager引数無しのクエリを使用しないこと(save,find等。findOneはentityManager引数必須)
                                                    // トランザクション内では、エラー時roll-backさせるためexceptionを出す(save,cancel処理含む)。try-catch使用注意
                                                    Asset quoteAsset =
                                                            assetService.findOrCreate(
                                                                    user.getId(),
                                                                    quoteCurrency,
                                                                    entityManager);
                                                    Asset baseAsset =
                                                            assetService.findOrCreate(
                                                                    user.getId(),
                                                                    baseCurrency,
                                                                    entityManager);

                                                    if (baseAsset == null || quoteAsset == null) {
                                                        throw new CustomException(
                                                                ErrorCode
                                                                        .ORDER_ERROR_ASSET_NOT_FOUND,
                                                                "userId: "
                                                                        + JsonUtil.encode(
                                                                                user.getId())
                                                                        + ", symbol: "
                                                                        + JsonUtil.encode(symbol)
                                                                        + ", baseAsset: "
                                                                        + JsonUtil.encode(baseAsset)
                                                                        + ", quoteAsset: "
                                                                        + JsonUtil.encode(
                                                                                quoteAsset));
                                                    }
                                                    // 利用可能額チェック
                                                    if (orderSide.isSell()) {
                                                        // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
                                                        if (baseAsset
                                                                        .getOnhandAmount()
                                                                        .subtract(
                                                                                baseAsset
                                                                                        .getLockedAmount())
                                                                        .compareTo(amountScaled)
                                                                < 0) {
                                                            throw new CustomException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                                                    "base_onhandAmount: "
                                                                            + JsonUtil.encode(
                                                                                    baseAsset
                                                                                            .getOnhandAmount())
                                                                            + ", base_lockedAmount: "
                                                                            + JsonUtil.encode(
                                                                                    baseAsset
                                                                                            .getLockedAmount())
                                                                            + ", base_remainingAmount: "
                                                                            + JsonUtil.encode(
                                                                                    baseAsset
                                                                                            .getOnhandAmount()
                                                                                            .subtract(
                                                                                                    baseAsset
                                                                                                            .getLockedAmount()))
                                                                            + ", amountScaled: "
                                                                            + JsonUtil.encode(
                                                                                    amountScaled)
                                                                            + ", userId: "
                                                                            + JsonUtil.encode(
                                                                                    user.getId())
                                                                            + ", form: "
                                                                            + JsonUtil.encode(
                                                                                    form));
                                                        }
                                                    } else {
                                                        if (quoteAsset
                                                                        .getOnhandAmount()
                                                                        .subtract(
                                                                                quoteAsset
                                                                                        .getLockedAmount())
                                                                        .compareTo(
                                                                                assetAmountScaled)
                                                                < 0) {
                                                            throw new CustomException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                                                    "base_onhandAmount: "
                                                                            + JsonUtil.encode(
                                                                                    baseAsset
                                                                                            .getOnhandAmount())
                                                                            + ", base_lockedAmount: "
                                                                            + JsonUtil.encode(
                                                                                    baseAsset
                                                                                            .getLockedAmount())
                                                                            + ", base_remainingAmount: "
                                                                            + JsonUtil.encode(
                                                                                    baseAsset
                                                                                            .getOnhandAmount()
                                                                                            .subtract(
                                                                                                    baseAsset
                                                                                                            .getLockedAmount()))
                                                                            + ", amountScaled: "
                                                                            + JsonUtil.encode(
                                                                                    amountScaled)
                                                                            + ", userId: "
                                                                            + JsonUtil.encode(
                                                                                    user.getId())
                                                                            + ", form: "
                                                                            + JsonUtil.encode(
                                                                                    form));
                                                        }
                                                    }
                                                    // 1日の取引上限チェック：通貨単位
                                                    // 上限撤廃フラグが立っているときはスキップ
                                                    if (!user.isTradeUncapped()) {
                                                        BigDecimal baseAmountSumDay =
                                                                getOrderTradeAmountPerDay(
                                                                        amountScaled,
                                                                        baseCurrencyConfig,
                                                                        baseCurrency,
                                                                        user,
                                                                        entityManager);

                                                        if (baseAmountSumDay.compareTo(
                                                                        baseCurrencyConfig
                                                                                .getMaxOrderAmountPerDay())
                                                                > 0) {

                                                            throw new CustomException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                                                    "userId: "
                                                                            + JsonUtil.encode(
                                                                                    user.getId())
                                                                            + ", form: "
                                                                            + JsonUtil.encode(
                                                                                    form));
                                                        }

                                                        BigDecimal quoteAmountSumDay =
                                                                getOrderTradeAmountPerDay(
                                                                        assetAmountScaled,
                                                                        quoteCurrencyConfig,
                                                                        quoteCurrency,
                                                                        user,
                                                                        entityManager);

                                                        if (quoteAmountSumDay.compareTo(
                                                                        quoteCurrencyConfig
                                                                                .getMaxOrderAmountPerDay())
                                                                > 0) {

                                                            throw new CustomException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                                                    "userId: "
                                                                            + JsonUtil.encode(
                                                                                    user.getId())
                                                                            + ", form: "
                                                                            + JsonUtil.encode(
                                                                                    form));
                                                        }
                                                    }
                                                    if (orderSide.isSell()) {
                                                        // Base:注文数量でロック（通貨ペア単位の桁数処理後、通貨単位の桁数処理なし)
                                                        assetService.updateWithExternalLock(
                                                                user.getId(),
                                                                symbol.getCurrencyPair()
                                                                        .getBaseCurrency(),
                                                                BigDecimal.ZERO,
                                                                amountScaled,
                                                                entityManager);

                                                    } else {
                                                        // Quote:注文数量×板平均価格で算出した注文総額概算でロック(通貨ペア単位の桁数処理後、通貨単位の桁数処理なし)
                                                        assetService.updateWithExternalLock(
                                                                user.getId(),
                                                                symbol.getCurrencyPair()
                                                                        .getQuoteCurrency(),
                                                                BigDecimal.ZERO,
                                                                assetAmountScaled,
                                                                entityManager);
                                                    }

                                                    // asset更新後チェック：残資産マイナスでないこと
                                                    if (baseAsset
                                                                            .getOnhandAmount()
                                                                            .subtract(
                                                                                    baseAsset
                                                                                            .getLockedAmount())
                                                                            .signum()
                                                                    < 0
                                                            || quoteAsset
                                                                            .getOnhandAmount()
                                                                            .subtract(
                                                                                    quoteAsset
                                                                                            .getLockedAmount())
                                                                            .signum()
                                                                    < 0
                                                            || baseAsset.getOnhandAmount().signum()
                                                                    < 0
                                                            || baseAsset.getLockedAmount().signum()
                                                                    < 0
                                                            || quoteAsset.getOnhandAmount().signum()
                                                                    < 0
                                                            || quoteAsset.getLockedAmount().signum()
                                                                    < 0) {

                                                        throw new CustomException(
                                                                ErrorCode.ORDER_ERROR_INVALID_ASSET,
                                                                "baseOnhandAmount: "
                                                                        + JsonUtil.encode(
                                                                                baseAsset
                                                                                        .getOnhandAmount())
                                                                        + ", baseLockedAmount: "
                                                                        + JsonUtil.encode(
                                                                                baseAsset
                                                                                        .getLockedAmount())
                                                                        + ", quoteOnhandAmount: "
                                                                        + JsonUtil.encode(
                                                                                quoteAsset
                                                                                        .getOnhandAmount())
                                                                        + ", quoteLockedAmount: "
                                                                        + JsonUtil.encode(
                                                                                quoteAsset
                                                                                        .getLockedAmount())
                                                                        + ", userId: "
                                                                        + JsonUtil.encode(
                                                                                user.getId())
                                                                        + ", form: "
                                                                        + JsonUtil.encode(form));
                                                    }
                                                    PosOrder posOrder = newEntity();
                                                    posOrder.setProperties(
                                                            symbol.getId(),
                                                            user.getId(),
                                                            amountScaled);
                                                    posOrder.setAnotherProperties(
                                                            orderSide,
                                                            orderType,
                                                            orderChannel,
                                                            price,
                                                            mmPrice,
                                                            amountScaled,
                                                            PosOrderStatus.WAITING,
                                                            OrderOperator.USER,
                                                            form.notes,
                                                            UserIdType.Invest);
                                                    this.save(posOrder, entityManager);
                                                    return posOrder;
                                                });
                                    });
                        });
    }

    private BigDecimal getOrderTradeAmountPerDay(
            BigDecimal amount,
            CurrencyConfig currencyConfig,
            Currency curerncy,
            User user,
            EntityManager entityManager) {

        if (curerncy == Currency.JPY) {
            return BigDecimal.ZERO;
        }

        // 処理概要:
        // ・発注数量(発注総額) + 当日の未約定指値の残数量 + 当日取引履歴の数量合算が1日の取引上限を超えているか判定する
        // 備考：
        // ・指値に価格変更はないのでcreated_atでも問題ない
        // ・入力amount = baseのときはamount、quoteのときはassetAmount ※桁数処理後
        // 処理内容:
        // ・JPYはチェック対象外
        // ・入力のBASE, QUOTEともに、BASE/QUOTEそれぞれ参照し、同一通貨の場合合算する
        // ・買いも売りも正で合算する
        // ・成行・指値ともカウントする
        // ・JPYはチェック対象外
        // 例：
        // ・BTC/JPY では 買い注文と売り注文でBTCの数量をカウントする。JPYは不要。(例: 1BTC買いで BTCは+1 )
        // ・ETH/BTCは 買い注文と売り注文で通貨(BTCとETH)毎に数量をカウントする。(例: 1ETH(0.04BTC)の買いで、ETHは+1、BTCは+0.04 )
        // ・ETH/BTCの場合、BASE:ETH/BTCとETH/JPYのETHと、QUOTE:ETH/BTCとBTC/JPYのBTCそれぞれの合算で上限チェックを行う

        // 日本時間の00:00以上、翌日00:00未満をUTCのミリ秒に変換(TABLE上はUTCタイムスタンプ保持)
        Date date = new Date();
        long dateFromTmp =
                date.toInstant()
                        .atZone(ZoneId.of("Asia/Tokyo"))
                        .truncatedTo(ChronoUnit.DAYS)
                        .withZoneSameInstant(ZoneId.of("UTC"))
                        .toInstant()
                        .toEpochMilli();
        long dateToTmp =
                date.toInstant()
                        .atZone(ZoneId.of("Asia/Tokyo"))
                        .plus(1, ChronoUnit.DAYS)
                        .truncatedTo(ChronoUnit.DAYS)
                        .withZoneSameInstant(ZoneId.of("UTC"))
                        .toInstant()
                        .toEpochMilli();

        // marketMaker用はログ抑制(appのみに実装)
        Date dateFrom = new Date(dateFromTmp);
        Date dateTo = new Date(dateToTmp);

        BigDecimal amountSumDay = BigDecimal.ZERO;
        BigDecimal amountPosTrade = BigDecimal.ZERO;
        BigDecimal amountPosOrder = BigDecimal.ZERO;
        List<Symbol> symbols = symbolService.findAllByCondition(TradeType.INVEST, entityManager);
        if (symbols != null) {
            for (Symbol symbolData : symbols) {
                // 当日取引履歴の同一通貨分サマリ
                List<PosTrade> posTrades =
                        posTradeService.findByCondition(
                                symbolData.getId(), user.getId(), dateFrom, dateTo, entityManager);
                if (posTrades != null) {
                    for (PosTrade posTrade : posTrades) {
                        if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {
                            amountPosTrade = amountPosTrade.add(posTrade.getAmount());
                        }
                        if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {
                            amountPosTrade =
                                    amountPosTrade.add(
                                            posTrade.getAmount().multiply(posTrade.getPrice()));
                        }
                    }
                }
                // 当日未約定指値の同一通貨分サマリ
                List<PosOrderStatus> orderStatuses = new ArrayList<>();
                orderStatuses.add(PosOrderStatus.WAITING);
                orderStatuses.add(PosOrderStatus.PARTIALLY_FILLED);
                List<PosOrder> posOrders =
                        this.findByCondition(
                                symbolData.getId(),
                                user.getId(),
                                null,
                                dateFrom,
                                dateTo,
                                orderStatuses,
                                entityManager);
                if (posOrders != null) {
                    for (PosOrder posOrder : posOrders) {
                        if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {
                            amountPosOrder = amountPosOrder.add(posOrder.getRemainingAmount());
                        }
                        if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {
                            amountPosOrder =
                                    amountPosOrder.add(
                                            posOrder.getRemainingAmount()
                                                    .multiply(posOrder.getPrice()));
                        }
                    }
                }
            }
        }
        // 合算(発注数量(総額) + 当日取引履歴の約定数量(総額) + 当日指値の発注数量(総額))
        return amountSumDay.add(amount).add(amountPosTrade).add(amountPosOrder);
    }

    public List<PosOrder> findByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Date dateFrom,
            Date dateTo,
            List<PosOrderStatus> orderStatuses,
            EntityManager entityManager) {
        return new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
            @Override
            public List<PosOrder> query() {
                return getResultList(
                        entityManager,
                        criteriaQuery,
                        root,
                        createPredicatesOfFindByCondition(
                                criteriaBuilder,
                                root,
                                symbolId,
                                CollectionUtils.isEmpty(orderStatuses)
                                        ? null
                                        : orderStatuses.toArray(
                                                new PosOrderStatus[orderStatuses.size()]),
                                null,
                                null,
                                null,
                                null,
                                null,
                                userId,
                                null,
                                null,
                                id,
                                null,
                                null,
                                dateFrom != null ? dateFrom.getTime() : null,
                                dateTo != null ? dateTo.getTime() : null),
                        0,
                        Integer.MAX_VALUE,
                        criteriaBuilder.asc(root.get(Order_.id)));
            }
        }.execute(getEntityClass(), entityManager);
    }

    private List<Predicate> createPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PosOrder> root,
            Long symbolId,
            PosOrderStatus[] orderStatuses,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderSide orderSide,
            OrderChannel orderChannel,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo) {
        List<Predicate> predicates =
                createPredicates(
                        criteriaBuilder,
                        root,
                        symbolId,
                        orderStatuses,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        orderSide,
                        userId,
                        null,
                        userIds,
                        exceptUserIds);

        if (orderChannel != null) {
            predicates.add(predicate.equalOrderChannel(criteriaBuilder, root, orderChannel));
        }

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }

            if (idTo != null) {
                predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
            }

            if (dateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(
                                criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
                predicates.add(
                        predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }
        }

        return predicates;
    }

    // インデックス: symbol_id, order_status, order_type, order_side, user_id, covered
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<PosOrder> root,
            Long symbolId,
            PosOrderStatus[] orderStatuses,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderSide orderSide,
            Long userId,
            Boolean covered,
            List<Long> userIds,
            List<Long> exceptUserIds) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

        if (!ArrayUtils.isEmpty(orderStatuses)) {
            predicates.add(predicate.inOrderStatus(root, orderStatuses));
        }

        if (orderType != null) {
            predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
        } else if (!ArrayUtils.isEmpty(orderTypes)) {
            predicates.add(predicate.inOrderType(root, orderTypes));
        } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
            predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
        }

        if (orderSide != null) {
            predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
        }

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        } else {
            // userIdsとexceptUserIdsは同時に利用される場合あり（重複時はexceptUserIdsが優先）
            if (userIds != null) {
                predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
            }

            if (exceptUserIds != null) {
                predicates.add(predicate.inExceptUserIds(criteriaBuilder, root, exceptUserIds));
            }
        }

        if (covered != null) {
            predicates.add(predicate.equalOrderCovered(criteriaBuilder, root, covered));
        }

        return predicates;
    }

    private BigDecimal calculateAssetAmount(Symbol symbol, BigDecimal amountWithoutScale) {

        // 約定総額算出
        // 約定価格×約定数量(部分約定含む)
        // 資産計算は桁数処理を行う（切り上げ）

        int assetPrecision = symbol.getCurrencyPair().getAssetPrecision();

        // 指定桁数+1桁で切り上げ
        // 指定桁数2桁のとき、4桁(指定桁数+2)で切り捨てしてから、3桁(指定桁数+1)で切り上げ
        // ケース1(未満) fee少数部 < min :0.0009 => 0.000 => 0
        // ケース2(同値) fee少数部 = min :0.001 => 0.01
        // ケース3(超過) fee少数部 > min :0.002 => 0.01

        // ケース1(未満) fee少数部 < min :0.1009 => 0.1
        // ケース2(同値) fee少数部 = min :0.101 => 0.11
        // ケース3(超過) fee少数部 > min :0.102 => 0.11

        // ケース1(未満) fee少数部 < min :123.0009 => 123
        // ケース2(同値) fee少数部 = min :123.001 => 123.01
        // ケース3(超過) fee少数部 > min :123.002 => 123.01

        // 指定桁数+2桁で切り捨てしてから 指定桁数+1桁で切り上げ
        BigDecimal assetAmountWithoutScalePlus =
                amountWithoutScale.setScale(assetPrecision + 1, RoundingMode.DOWN);
        BigDecimal assetAmountScaled =
                symbol.getCurrencyPair()
                        .getScaledAsset(assetAmountWithoutScalePlus, RoundingMode.UP);

        return assetAmountScaled;
    }

    public void sendOrderMail(
            PosOrder posOrder,
            MailNoreplyService mailNoreplyService,
            SesManager sesManager,
            Symbol symbol,
            User user)
            throws Exception {
        if (user == null || user.isTradeUncapped()) {
            return;
        }
        try {
            // get mail template
            var mailInfo = mailNoreplyService.findOne(MailNoreplyType.POS_ORDER);
            var mailTemplate = mailInfo.getContents();
            mailTemplate =
                    mailTemplate.replace("${instrumentId}", symbol.getCurrencyPair().getName());
            mailTemplate = mailTemplate.replace("${side}", posOrder.getOrderSide().toString());
            mailTemplate = mailTemplate.replace("${orderType}", posOrder.getOrderType().toString());
            mailTemplate =
                    mailTemplate.replace(
                            "${size}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(posOrder.getAmount())
                                            .toPlainString()));
            mailTemplate =
                    mailTemplate.replace(
                            "${price}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledPrice(posOrder.getPrice())
                                            .toPlainString()));
            mailTemplate = mailTemplate.replace("${orderId}", posOrder.getId().toString());
            // send mail
            sesManager.send(
                    mailInfo.getFromAddress(), user.getEmail(), mailInfo.getTitle(), mailTemplate);
        } catch (Exception e) {
            log.error("sendOrderMail error", e);
        }
    }

    private void checkInsiderAndRisker(PosOrder posOrder) {
        User user = userService.findOne(posOrder.getUserId());
        StringBuilder stringBuilder = new StringBuilder();

        if (user.isInsider()) {
            stringBuilder.append("insider ordered. userId: " + user.getId() + ".\n");
        }

        if (user.isRisker()) {
            stringBuilder.append("risker ordered. userId: " + user.getId() + ".\n");
        }

        if (stringBuilder.length() > 0) {
            stringBuilder.append(
                    "symbolId: "
                            + posOrder.getSymbolId()
                            + ", orderSide: "
                            + posOrder.getOrderSide()
                            + ", orderType: "
                            + posOrder.getOrderType()
                            + ", price: "
                            + posOrder.getPrice().toPlainString()
                            + ", amount: "
                            + posOrder.getAmount().toPlainString()
                            + ".");
            log.info(stringBuilder.toString());
        }
    }

    public PageData<PosOrder> findByConditionPageData(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide,
            Integer number,
            Integer size,
            boolean isAscending) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<PosOrder>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                CollectionUtils.isEmpty(orderStatuses)
                                                        ? null
                                                        : orderStatuses.toArray(
                                                                new PosOrderStatus
                                                                        [orderStatuses.size()]),
                                                orderType,
                                                CollectionUtils.isEmpty(orderTypes)
                                                        ? null
                                                        : orderTypes.toArray(
                                                                new OrderType[orderTypes.size()]),
                                                CollectionUtils.isEmpty(exceptOrderTypes)
                                                        ? null
                                                        : exceptOrderTypes.toArray(
                                                                new OrderType
                                                                        [exceptOrderTypes.size()]),
                                                orderSide,
                                                null,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo));
                            }
                        });
        return new PageData<PosOrder>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                            @Override
                            public List<PosOrder> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                CollectionUtils.isEmpty(orderStatuses)
                                                        ? null
                                                        : orderStatuses.toArray(
                                                                new PosOrderStatus
                                                                        [orderStatuses.size()]),
                                                orderType,
                                                CollectionUtils.isEmpty(orderTypes)
                                                        ? null
                                                        : orderTypes.toArray(
                                                                new OrderType[orderTypes.size()]),
                                                CollectionUtils.isEmpty(exceptOrderTypes)
                                                        ? null
                                                        : exceptOrderTypes.toArray(
                                                                new OrderType
                                                                        [exceptOrderTypes.size()]),
                                                orderSide,
                                                null,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo),
                                        number,
                                        size,
                                        isAscending
                                                ? criteriaBuilder.asc(root.get(Order_.id))
                                                : criteriaBuilder.desc(root.get(Order_.id)));
                            }
                        }));
    }

    public List<PosOrder> findByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide,
            Integer number,
            Integer size,
            boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        CollectionUtils.isEmpty(orderStatuses)
                                                ? null
                                                : orderStatuses.toArray(
                                                        new PosOrderStatus[orderStatuses.size()]),
                                        orderType,
                                        CollectionUtils.isEmpty(orderTypes)
                                                ? null
                                                : orderTypes.toArray(
                                                        new OrderType[orderTypes.size()]),
                                        CollectionUtils.isEmpty(exceptOrderTypes)
                                                ? null
                                                : exceptOrderTypes.toArray(
                                                        new OrderType[exceptOrderTypes.size()]),
                                        orderSide,
                                        null,
                                        userId,
                                        null,
                                        null,
                                        id,
                                        idFrom,
                                        idTo,
                                        dateFrom,
                                        dateTo),
                                number,
                                size,
                                isAscending
                                        ? criteriaBuilder.asc(root.get(Order_.id))
                                        : criteriaBuilder.desc(root.get(Order_.id)));
                    }
                });
    }

    public List<PosOrder> findAllByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            OrderSide orderSide) {
        return new ArrayList<PosOrder>(
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                            @Override
                            public List<PosOrder> query() {
                                List<Predicate> predicates =
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                CollectionUtils.isEmpty(orderStatuses)
                                                        ? null
                                                        : orderStatuses.toArray(
                                                                new PosOrderStatus
                                                                        [orderStatuses.size()]),
                                                orderType,
                                                null,
                                                null,
                                                orderSide,
                                                null,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(Order_.id)));
                            }
                        }));
    }

    // BO管理画面専用、orderChannel検索条件が必要、さらに、ユーザーIDが複数入力可能です。
    public List<PosOrder> findByConditionForBo(
            Long symbolId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide,
            OrderChannel orderChannel,
            Integer number,
            Integer size,
            boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        CollectionUtils.isEmpty(orderStatuses)
                                                ? null
                                                : orderStatuses.toArray(
                                                        new PosOrderStatus[orderStatuses.size()]),
                                        orderType,
                                        CollectionUtils.isEmpty(orderTypes)
                                                ? null
                                                : orderTypes.toArray(
                                                        new OrderType[orderTypes.size()]),
                                        CollectionUtils.isEmpty(exceptOrderTypes)
                                                ? null
                                                : exceptOrderTypes.toArray(
                                                        new OrderType[exceptOrderTypes.size()]),
                                        orderSide,
                                        orderChannel,
                                        null,
                                        userIds,
                                        exceptUserIds,
                                        id,
                                        idFrom,
                                        idTo,
                                        dateFrom,
                                        dateTo));
                    }
                });
    }

    public List<PosOrder> findAllFromHistory(
            Symbol symbol,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Date createdAtFrom,
            Date createdAtTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSqlString(
                        mapSqlParameterSource,
                        symbol,
                        userId,
                        null,
                        null,
                        id,
                        idFrom,
                        idTo,
                        createdAtFrom,
                        createdAtTo,
                        orderStatuses,
                        orderSide,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        null);

        log.info("redshift-sql:" + sql.toString());
        log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    // BO管理画面専用、orderChannel検索条件が必要、さらに、ユーザーIDが複数入力可能です。
    public List<PosOrder> findAllFromHistoryForBo(
            Symbol symbol,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Date createdAtFrom,
            Date createdAtTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            OrderSide orderSide,
            OrderChannel orderChannel) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSqlString(
                        mapSqlParameterSource,
                        symbol,
                        null,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        createdAtFrom,
                        createdAtTo,
                        orderStatuses,
                        orderSide,
                        orderType,
                        null,
                        null,
                        orderChannel);

        log.info("redshift-sql:" + sql.toString());
        log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    public List<PosOrder> findFromHistoryByOrderIds(List<Long> orderIds) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("orderIds", orderIds);
        return historicalTransactionManager.findFromHistory(
                "select * from pos_order where id in (:orderIds)",
                mapSqlParameterSource,
                newRowMapper());
    }

    private StringBuilder createSqlString(
            MapSqlParameterSource mapSqlParameterSource,
            Symbol symbol,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Date createdAtFrom,
            Date createdAtTo,
            List<PosOrderStatus> orderStatuses,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderChannel orderChannel) {

        StringBuilder sql = new StringBuilder("select * from pos_order");

        // 条件指定
        sql.append(" where symbol_id = :symbol_id");
        mapSqlParameterSource.addValue("symbol_id", symbol.getId());

        if (id != null) {
            sql.append(" and id = :id");
            mapSqlParameterSource.addValue("id", id);
        } else {

            if (userId != null) {
                sql.append(" and user_id = :user_id");
                mapSqlParameterSource.addValue("user_id", userId);
            } else {
                if (userIds != null) {
                    sql.append(" and user_id in (:user_ids)");
                    mapSqlParameterSource.addValue("user_ids", userIds);
                }

                if (exceptUserIds != null) {
                    sql.append(" and user_id not in (:except_user_ids)");
                    mapSqlParameterSource.addValue("except_user_ids", exceptUserIds);
                }
            }

            if (idFrom != null) {
                sql.append(" and id >= :id_from");
                mapSqlParameterSource.addValue("id_from", idFrom);
            }

            if (idTo != null) {
                sql.append(" and id <= :id_to");
                mapSqlParameterSource.addValue("id_to", idTo);
            }

            if (createdAtFrom != null) {
                sql.append(" and created_at >= :created_at_from");
                mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
            }

            if (createdAtTo != null) {
                sql.append(" and created_at < :created_at_to");
                mapSqlParameterSource.addValue("created_at_to", createdAtTo);
            }

            if (orderStatuses != null && !orderStatuses.isEmpty()) {
                sql.append(" and order_status in (:order_statuses)");
                mapSqlParameterSource.addValue(
                        "order_statuses",
                        orderStatuses.stream()
                                .map((orderStatus) -> orderStatus.name())
                                .collect(Collectors.toList()));
            }

            if (orderSide != null) {
                sql.append(" and order_side = :order_side");
                mapSqlParameterSource.addValue("order_side", orderSide.toString());
            }

            if (orderType != null) {
                sql.append(" and order_type = :order_type");
                mapSqlParameterSource.addValue("order_type", orderType.toString());
            } else {
                if (!CollectionUtils.isEmpty(orderTypes)) {
                    sql.append(" and order_type in (:order_types)");
                    mapSqlParameterSource.addValue(
                            "order_types",
                            orderTypes.stream()
                                    .map((type) -> type.name())
                                    .collect(Collectors.toList()));
                }

                if (!CollectionUtils.isEmpty(exceptOrderTypes)) {
                    sql.append(" and order_type not in (:except_order_types)");
                    mapSqlParameterSource.addValue(
                            "except_order_types",
                            exceptOrderTypes.stream()
                                    .map((type) -> type.name())
                                    .collect(Collectors.toList()));
                }
            }

            if (orderChannel != null) {
                sql.append(" and order_channel = :order_channel");
                mapSqlParameterSource.addValue("order_channel", orderChannel.toString());
            }
        }

        return sql;
    }

    public List<PosOrder> findByCondition(List<Long> symbolIds) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.inSymbolIds(criteriaBuilder, root, symbolIds));
                        predicates.add(
                                predicate.equalOrderCovered(criteriaBuilder, root, Boolean.FALSE));
                        predicates.add(
                                predicate.equalOrderStatus(
                                        criteriaBuilder, root, PosOrderStatus.WAITING));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<PosOrder> findByOrderIds(List<Long> orderIds) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.inIds(criteriaBuilder, root, orderIds));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<PosOrder> findBySymbolIds(List<Long> symbolIds) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.inSymbolIds(criteriaBuilder, root, symbolIds));
                        predicates.add(
                                predicate.equalOrderCovered(criteriaBuilder, root, Boolean.FALSE));
                        predicates.add(
                                predicate.equalOrderStatus(
                                        criteriaBuilder, root, PosOrderStatus.WAITING));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public PageData<PosOrder> createPageData(
            List<PosOrder> content, Long count, Integer number, Integer size) {
        List<PosOrder> pageContents = new ArrayList<PosOrder>();

        int maxSize =
                (number * size + size) > content.size() ? content.size() : (number * size + size);

        for (int i = number * size; i < maxSize; i++) {

            pageContents.add(content.get(i));
        }

        return new PageData<PosOrder>(number, size, count, pageContents);
    }

    public List<PosOrder> findByCondition(Long symbolId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        predicates.add(
                                predicate.equalOrderCovered(criteriaBuilder, root, Boolean.FALSE));
                        predicates.add(
                                predicate.equalOrderStatus(
                                        criteriaBuilder, root, PosOrderStatus.WAITING));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                0,
                                300,
                                criteriaBuilder.asc(root.get(PosOrder_.createdAt)));
                    }
                });
    }

    public void sendTradeMail(
            PosOrder posOrder,
            PosTrade posTrade,
            MailNoreplyService mailNoreplyService,
            SesManager sesManager,
            Symbol symbol,
            User user) {
        if (user == null || user.isTradeUncapped()) {
            return;
        }

        try {
            MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.POS_TRADE);
            String mailBody = mailNoreply.getContents();
            mailBody =
                    mailBody.replace("${instrumentId}", String.valueOf(symbol.getCurrencyPair()));
            mailBody = mailBody.replace("${orderId}", String.valueOf(posOrder.getId()));
            mailBody = mailBody.replace("${side}", String.valueOf(posOrder.getOrderSide()));
            mailBody = mailBody.replace("${orderType}", String.valueOf(posOrder.getOrderType()));
            mailBody =
                    mailBody.replace(
                            "${size}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(posOrder.getAmount())
                                            .toPlainString()));
            mailBody =
                    mailBody.replace(
                            "${price}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledPrice(posOrder.getPrice())
                                            .toPlainString()));
            mailBody =
                    mailBody.replace(
                            "${trade_size}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(posTrade.getAmount())
                                            .toPlainString()));
            mailBody =
                    mailBody.replace(
                            "${trade_price}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledPrice(posTrade.getPrice())
                                            .toPlainString()));
            mailBody =
                    mailBody.replace(
                            "${remaining_amount}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(posOrder.getRemainingAmount())
                                            .toPlainString()));
            // send mail
            sesManager.send(
                    mailNoreply.getFromAddress(),
                    user.getEmail(),
                    mailNoreply.getTitle(),
                    mailBody);
        } catch (Exception e) {
            log.error("sendTradeMail error", e);
        }
    }

    public void archive(Symbol symbol) {
        final int archiveSizeLimit = 1000;

        // id昇順ソート済み
        List<PosOrder> posOrders = findInactive(symbol.getId(), archiveSizeLimit);

        if (CollectionUtils.isEmpty(posOrders)) {
            return;
        }

        // 処理：最大idをarchive(削除)対象から除外する ※事前にid昇順ソート済み
        // 目的：Aurora fail over時のauto-increment値がPKの最大ID+1にリセットされる問題対応のため、
        // 削除時に最大IDを1件処理せずに残しておく
        posOrders.remove(posOrders.size() - 1);

        if (CollectionUtils.isEmpty(posOrders)) {
            return;
        }

        StringJoiner stringJoiner = new StringJoiner(",");
        List<Long> ids = new ArrayList<>();
        posOrders.forEach(
                posOrder -> {
                    StringJoiner joiner = new StringJoiner(", ", "(", ")");
                    joiner.add(String.valueOf(posOrder.getId()))
                            .add(String.valueOf(posOrder.getSymbolId()))
                            .add(String.valueOf(posOrder.getUserId()))
                            .add("'" + posOrder.getOrderSide().name() + "'")
                            .add("'" + posOrder.getOrderType().name() + "'")
                            .add("'" + posOrder.getOrderChannel().name() + "'")
                            .add(
                                    posOrder.getPrice() != null
                                            ? posOrder.getPrice().toPlainString()
                                            : "null")
                            .add(posOrder.getMmPrice().toPlainString())
                            .add(posOrder.getAmount().toPlainString())
                            .add(posOrder.getRemainingAmount().toPlainString())
                            .add("'" + posOrder.getOrderStatus().name() + "'")
                            .add(
                                    posOrder.getCreatedAt() != null
                                            ? "'"
                                                    + FormatUtil.format(
                                                            posOrder.getCreatedAt(),
                                                            FormatUtil.FormatPattern
                                                                    .YYYY_MM_DD_HH_MM_SS_S)
                                                    + "'"
                                            : "null")
                            .add(
                                    posOrder.getUpdatedAt() != null
                                            ? "'"
                                                    + FormatUtil.format(
                                                            posOrder.getUpdatedAt(),
                                                            FormatUtil.FormatPattern
                                                                    .YYYY_MM_DD_HH_MM_SS_S)
                                                    + "'"
                                            : "null")
                            .add("'" + posOrder.getOrderOperator().name() + "'")
                            .add(String.valueOf(posOrder.getCovered()))
                            .add(
                                    posOrder.getNotes() != null
                                            ? "'" + posOrder.getNotes() + "'"
                                            : "null")
                            .add("'" + posOrder.getIdType().toString() + "'");
                    stringJoiner.add(joiner.toString());
                    ids.add(posOrder.getId());
                });

        historicalTransactionManager.archive(
                "insert into "
                        + "pos_order"
                        + " (id, symbol_id, user_id, order_side, order_type, order_channel, price, mm_price, amount, remaining_amount, order_status, created_at, updated_at, order_operator, covered, notes, id_type) values "
                        + stringJoiner);

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("ids", ids);
        customTransactionManager.multiUpdate(
                "delete from pos_order where id in (:ids)", mapSqlParameterSource);

        log.info("archive_log_pos_order,{}", ids);
    }

    public List<PosOrder> findInactive(Long symbolId, int limit) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                    @Override
                    public List<PosOrder> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                createPredicates(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        PosOrderStatus.INACTIVE_ORDER_STATUSES,
                                        null,
                                        null,
                                        null,
                                        null,
                                        null,
                                        true,
                                        null,
                                        null),
                                0,
                                limit,
                                criteriaBuilder.asc(root.get(PosOrder_.id)));
                    }
                });
    }

    public PosOrder findByPosOrder(Long userId, Long symbolId, Long id) {
        if (symbolId == null && id == null) {
            return new PosOrder();
        }
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PosOrder, PosOrder>() {
                    @Override
                    public PosOrder query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (symbolId != null) {
                            predicates.add(
                                    predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        }
                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }
                        if (id != null) {
                            predicates.add(predicate.equalId(criteriaBuilder, root, id));
                        }
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public PosOrder findFromHistoryByOrder(Long userId, Long symbolId, Long id) {
        if (symbolId == null && id == null) {
            return new PosOrder();
        }

        StringBuilder sql = new StringBuilder("SELECT * FROM pos_order WHERE 1=1");
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        if (userId != null) {
            sql.append(" AND user_id = :userId");
            mapSqlParameterSource.addValue("userId", userId);
        }
        if (symbolId != null) {
            sql.append(" AND symbol_id = :symbolId");
            mapSqlParameterSource.addValue("symbolId", symbolId);
        }
        if (id != null) {
            sql.append(" AND id = :id");
            mapSqlParameterSource.addValue("id", id);
        }

        return historicalTransactionManager.findSingleFromHistoryWithMultipleConditions(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    public void archive(PosOrder posOrder) {
        String sql = "UPDATE pos_order SET notes = ? WHERE id = ?";
        log.info("update_archive_log_pos_order, {}", sql);
        PreparedStatementSetter preparedStatementSetter =
                ps -> {
                    ps.setString(1, posOrder.getNotes());
                    ps.setLong(2, posOrder.getId());
                };
        historicalTransactionManager.archiveWithParameter(sql, preparedStatementSetter);
    }

    // CurrencyPairConfigから、サーキットブレーカー中かチェック
    // OrderTypeのチェックは呼び出し元で行う
    public boolean isCircuitBreaking(Symbol symbol) {
        Long nowLong = new Date().getTime();
        CurrencyPairConfig config =
                currencyPairConfigService.findByCondition(
                        symbol.getTradeType(), symbol.getCurrencyPair());
        // 通貨ペア設定、サーキットブレーカー開始時間、サーキットブレーカー持続時間のいずれかnullならチェックしない
        if (config == null
                || config.getCircuitBreakUpdatedAt() == null
                || config.getCircuitBreakStopTimespan() == null) {
            return false;
        }
        log.info(
                "nowLong: {}, circuitBreakUpdatedAt: {}, circuitBreakStopTimespan:{}, symBol:{}",
                nowLong,
                config.getCircuitBreakUpdatedAt().getTime(),
                config.getCircuitBreakStopTimespan() * DateUnit.MINUTE.getMillis(),
                JsonUtil.encode(symbol));
        return (nowLong - config.getCircuitBreakUpdatedAt().getTime())
                < config.getCircuitBreakStopTimespan() * DateUnit.MINUTE.getMillis();
    }

    public Optional<PosOrder> findByCondition(
            Long symbolId, OrderSide orderSide, UserIdType userIdType) {
        PosOrder entity =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PosOrder, PosOrder>() {
                            @Override
                            public PosOrder query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                                predicates.add(
                                        predicate.equalOrderSide(criteriaBuilder, root, orderSide));
                                predicates.add(
                                        predicate.equalIdType(criteriaBuilder, root, userIdType));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        return Optional.ofNullable(entity);
    }
}

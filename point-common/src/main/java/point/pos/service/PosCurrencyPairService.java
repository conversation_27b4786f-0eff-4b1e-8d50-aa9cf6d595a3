package point.pos.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.component.DataSourceManager;
import point.common.constant.CurrencyPair;
import point.common.model.dto.CurrencyPairDTO;
import point.common.model.response.PosCurrencyPair;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosCurrencyPairService {

    private final DataSourceManager dataSourceManager;

    public List<PosCurrencyPair> getCurrencyPair(String tradeType) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        List<PosCurrencyPair> pairs = new ArrayList<>();
        try {
            Query query = entityManager.createNamedQuery("findCurrencyPairs");
            query.setParameter("tradeType", tradeType);
            List<CurrencyPairDTO> resultList = query.getResultList();
            return resultList.stream().map(this::mapToPosCurrencyPair).collect(Collectors.toList());
        } finally {
            entityManager.close();
        }
    }

    private PosCurrencyPair mapToPosCurrencyPair(CurrencyPairDTO dto) {
        CurrencyPair currencyPair = CurrencyPair.valueOf(dto.getCurrencyPair());
        return PosCurrencyPair.builder()
                .symbolId(dto.getId())
                .tradeType(dto.getTradeType())
                .currencyPair(dto.getCurrencyPair())
                .minOrderAmount(dto.getMinOrderAmount())
                .maxOrderAmount(dto.getMaxOrderAmount())
                .posSlippagepercent(dto.getPosSlippagePercent())
                .posSpreadPercent(dto.getPosSpreadPercent())
                .quotePrecision(currencyPair.getQuotePrecision())
                .basePrecision(currencyPair.getPosBasePrecision())
                .precision(currencyPair.getAssetPrecision())
                .enabled(dto.getEnabled())
                .build();
    }
}

package point.common.http.cb;

public final class HttpHeader {

    private HttpHeader() {}

    public static final String CONTENT_TYPE = "Content-Type";
    public static final String CONTENT_LENGTH = "Content-Length";

    public static final String ACCEPT_CHARSET = "Accept-Charset";

    public static final String ACCEPT_ENCODING = "Accept-Encoding";

    public static final String CONTENT_ENCODING = "Content-Encoding";

    public static final String GZIP_VALUE = "gzip";

    public static final String APPLICATION_JSON_VALUE = "application/json";

    public static final String APPLICATION_X_WWW_FORM_URLENCODED_VALUE =
            "application/x-www-form-urlencoded";
}

package point.common.util;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;

public class BindingResultUtil {

    @Getter List<String> errorList;

    public boolean hasErrors(BindingResult result) {
        if (result.hasErrors()) {
            errorList = new ArrayList<String>();
            for (ObjectError error : result.getAllErrors()) {
                errorList.add(error.getDefaultMessage());
            }
            return true;
        }
        return false;
    }
}

package point.common.util;

import org.springframework.core.env.Environment;

public final class EnvironmentUtil {

    private EnvironmentUtil() {}

    public static String getActiveProfileOrDefault(Environment env) {
        if (env != null) {
            String[] activeProfiles = env.getActiveProfiles();
            if (activeProfiles.length > 0) {
                String firstProfile = activeProfiles[0];
                if (point.common.constant.Environment.local.name().equalsIgnoreCase(firstProfile)) {
                    return "default";
                }
                return firstProfile;
            }
        }
        return "default";
    }
}

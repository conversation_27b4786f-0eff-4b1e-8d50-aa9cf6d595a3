package point.common.util;

import java.util.regex.Pattern;
import point.common.constant.LineFeed;

public class StringUtil {

    private static final Pattern EMAIL_PATTERN =
            Pattern.compile("^\\w+[\\w\\._\\-\\+]*@\\w+[\\w\\._\\-]*\\.\\w+$");

    private static final Pattern EMAIL_PATTERN_EXCEPT_ALIAS =
            Pattern.compile("^\\w+[\\w\\._\\-]*@\\w+[\\w\\._\\-]*\\.\\w+$");

    private static final Pattern PHONE_NUMBER_PATTERN = Pattern.compile("^[0][0-9]+$");

    public static String toUpperCamelCase(String source) {
        StringBuilder stringBuilder = new StringBuilder();

        for (String part : source.toLowerCase().split("_")) {
            stringBuilder.append(String.valueOf(part.charAt(0)).toUpperCase() + part.substring(1));
        }

        return stringBuilder.toString();
    }

    public static String toLowerCamelCase(String source) {
        StringBuilder stringBuilder = new StringBuilder();

        for (String part : source.toLowerCase().split("_")) {
            stringBuilder.append(
                    stringBuilder.length() == 0
                            ? part
                            : String.valueOf(part.charAt(0)).toUpperCase() + part.substring(1));
        }

        return stringBuilder.toString();
    }

    public static boolean validateEmail(String email) {
        return EMAIL_PATTERN.matcher(email).find();
    }

    public static boolean validateEmailExceptAlias(String email) {
        return EMAIL_PATTERN_EXCEPT_ALIAS.matcher(email).find();
    }

    public static boolean validatePhoneNumber(String phoneNumber) {
        return PHONE_NUMBER_PATTERN.matcher(phoneNumber).find()
                && 10 <= phoneNumber.length()
                && phoneNumber.length() <= 11;
    }

    public static boolean validatePersonalPhoneNumber(String phoneNumber) {
        return PHONE_NUMBER_PATTERN.matcher(phoneNumber).find() && phoneNumber.length() == 11;
    }

    public static String replaceLineFeedToHtml(String source) {
        return source.replaceAll(LineFeed.TEXT.getValue(), LineFeed.HTML.getValue());
    }

    public static String createOtpauthUri(String issuer, String email, String secret) {
        return "otpauth://totp/" + issuer + ":" + email + "?secret=" + secret + "&issuer=" + issuer;
    }
}

package point.common.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;
import point.common.constant.UserAgreementType;

@Setter
@Getter
public class UserAgreementDTO {

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    private UserAgreementType userAgreementType;

    public UserAgreementDTO(Date updatedAt, UserAgreementType userAgreementType) {
        this.updatedAt = updatedAt;
        this.userAgreementType = userAgreementType;
    }
}

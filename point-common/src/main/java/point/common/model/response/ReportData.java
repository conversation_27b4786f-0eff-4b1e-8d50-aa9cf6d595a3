package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import point.common.constant.CurrencyPair;
import point.common.constant.OrderSide;
import point.common.constant.PosConstants;

@JsonIgnoreProperties(ignoreUnknown = true)
/*
 * Report data for dayly and Monthly
 */
public class ReportData implements Serializable {

    // 残高
    public static class AssetReport implements Serializable {

        private static final long serialVersionUID = -3198232666112886448L;

        @Getter @Setter private String amount;

        @Getter @Setter private String currency;

        public Integer getCurrencyIndex() {
            // Assetの通貨は以下の順で並べる
            switch (this.currency) {
                case "JPY":
                    return 2;
                default:
                    return 99;
            }
        }
    }

    // 約定履歴
    public static class TradeReport implements Serializable {

        private static final long serialVersionUID = 5051383513706533061L;

        @Getter @Setter private String id;

        @Getter @Setter private CurrencyPair currencyPair;

        @Getter @Setter private OrderSide orderSide;

        @Getter @Setter private String dateTime;

        @Getter @Setter private BigDecimal amount;

        @Getter @Setter private BigDecimal price;

        @Getter @Setter private BigDecimal paidAmount;

        @Getter @Setter private BigDecimal fee;

        @Getter @Setter private boolean isMediation;

        // 消費税額
        @Getter @Setter private BigDecimal consumptionTaxAmount;
    }

    // 販売所約定履歴
    public static class PosTradeReport implements Serializable {

        private static final long serialVersionUID = -5748901795328202711L;

        @Getter @Setter private String id;

        @Getter @Setter private String currencyPair;

        @Getter @Setter private OrderSide orderSide;

        @Getter @Setter private String dateTime;

        @Getter @Setter private BigDecimal amount;

        @Getter @Setter private BigDecimal price;

        @Getter @Setter private BigDecimal paidAmount;

        @Getter @Setter private BigDecimal fee;

        // 消費税額
        @Getter @Setter private BigDecimal consumptionTaxAmount;
    }

    // 入出金履歴
    public static class DepositWithdrawalReport implements Serializable {

        private static final long serialVersionUID = -9112110985723482816L;

        @Getter @Setter private String currency;

        @Getter @Setter private String id;

        @Getter @Setter private String dateTime;

        @Getter @Setter private String type;

        @Getter @Setter private BigDecimal depositAmount;

        @Getter @Setter private BigDecimal withdrawalAmount;

        @Getter @Setter private BigDecimal fee;

        // 同一時刻、同一通貨の入出金履歴は作成時に順序を決める
        @Getter @Setter private Integer sortIndex;

        // 消費税額
        @Getter @Setter private BigDecimal consumptionTaxAmount;

        // 手数料金額（日本円換算額）税込
        @Getter @Setter private BigDecimal commissionJpyConvert;

        // 日本元手数料
        @Getter @Setter private BigDecimal fiatFee;

        public Integer getCurrencyIndex() {
            // Deposit/Withdrawalの通貨は以下の順で並べる
            switch (this.currency) {
                case "JPY":
                case PosConstants.CURRENCY_JYP:
                    return 2;
                default:
                    return 99;
            }
        }
    }

    private static final long serialVersionUID = -6578180690915096869L;

    @Getter @Setter private Long userId;

    @Getter @Setter private String accountId;

    @Getter @Setter private String createdDay;

    @Getter @Setter private String firstDay;

    @Getter @Setter private String lastDay;

    @Getter @Setter private List<AssetReport> assetReportList;

    @Getter @Setter private List<PosTradeReport> posTradeReportList;

    @Getter @Setter private List<DepositWithdrawalReport> depositWithdrawalReportList;

    @Getter @Setter private String userName;

    // 取引手数料
    @Getter @Setter private BigDecimal transactionFees;

    // 入出金手数料
    @Getter @Setter private BigDecimal depositWithdrawalFees;

    // 合計
    @Getter @Setter private BigDecimal total;

    // 内消費税
    @Getter @Setter private BigDecimal consumptionTaxIncluded;
}

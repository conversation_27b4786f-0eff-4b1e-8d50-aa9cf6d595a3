package point.common.model.response;

import lombok.Getter;
import lombok.Setter;
import point.common.constant.ErrorCode;

@Getter
@Setter
public class GlobalApiResponse<T> {
    private static final int SUCCESS_CODE = 200;
    private static final int BAD_REQUEST_CODE = 400;
    private int code; // ステータスコード
    private String message; // メッセージ
    private T params; // データ

    // コンストラクタ
    public GlobalApiResponse(int code, T params) {
        this.code = code;
        this.params = params;
    }

    public GlobalApiResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static <T> GlobalApiResponse<T> success(T params) {
        return new GlobalApiResponse<>(SUCCESS_CODE, params);
    }

    public static <T> GlobalApiResponse<T> badRequest(String message) {
        return new GlobalApiResponse<>(BAD_REQUEST_CODE, message);
    }

    public static <T> GlobalApiResponse<T> badRequest(ErrorCode errorCode) {
        return new GlobalApiResponse<>(BAD_REQUEST_CODE, errorCode.getMessage());
    }
}

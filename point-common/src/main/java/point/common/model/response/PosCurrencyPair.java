package point.common.model.response;

import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PosCurrencyPair {

    private Long symbolId;

    private String tradeType;

    private String currencyPair;

    private BigDecimal minOrderAmount;

    private BigDecimal maxOrderAmount;

    private BigDecimal posSpreadPercent;

    private BigDecimal posSlippagepercent;

    private int precision;

    private int quotePrecision;

    private int basePrecision;

    private boolean enabled;
}

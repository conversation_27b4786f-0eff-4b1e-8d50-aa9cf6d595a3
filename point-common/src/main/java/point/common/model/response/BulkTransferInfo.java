package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** BulkTransferInfo */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BulkTransferInfo {
    @JsonAlias("itemId")
    private String itemId = null;

    @JsonAlias("beneficiaryBankCode")
    private String beneficiaryBankCode = null;

    @JsonAlias("beneficiaryBankName")
    private String beneficiaryBankName = null;

    @JsonAlias("beneficiaryBranchCode")
    private String beneficiaryBranchCode = null;

    @JsonAlias("beneficiaryBranchName")
    private String beneficiaryBranchName = null;

    @JsonAlias("clearingHouseName")
    private String clearingHouseName = null;

    @JsonAlias("accountTypeCode")
    private String accountTypeCode = null;

    @JsonAlias("accountNumber")
    private String accountNumber = null;

    @JsonAlias("beneficiaryName")
    private String beneficiaryName = null;

    @JsonAlias("transferAmount")
    private String transferAmount = null;

    @JsonAlias("newCode")
    private String newCode = null;

    @JsonAlias("ediInfo")
    private String ediInfo = null;

    @JsonAlias("transferDesignnatedType")
    private String transferDesignnatedType = null;

    @JsonAlias("identification")
    private String identification = null;

    @JsonAlias("transferDetailResponses")
    private List<TransferDetailResponse> transferDetailResponses = null;

    @JsonAlias("unableDetailInfos")
    private List<UnableDetailInfo> unableDetailInfos = null;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferInfo {\n");

        sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
        sb.append("    beneficiaryBankCode: ")
                .append(toIndentedString(beneficiaryBankCode))
                .append("\n");
        sb.append("    beneficiaryBankName: ")
                .append(toIndentedString(beneficiaryBankName))
                .append("\n");
        sb.append("    beneficiaryBranchCode: ")
                .append(toIndentedString(beneficiaryBranchCode))
                .append("\n");
        sb.append("    beneficiaryBranchName: ")
                .append(toIndentedString(beneficiaryBranchName))
                .append("\n");
        sb.append("    clearingHouseName: ")
                .append(toIndentedString(clearingHouseName))
                .append("\n");
        sb.append("    accountTypeCode: ").append(toIndentedString(accountTypeCode)).append("\n");
        sb.append("    accountNumber: ").append(toIndentedString(accountNumber)).append("\n");
        sb.append("    beneficiaryName: ").append(toIndentedString(beneficiaryName)).append("\n");
        sb.append("    transferAmount: ").append(toIndentedString(transferAmount)).append("\n");
        sb.append("    newCode: ").append(toIndentedString(newCode)).append("\n");
        sb.append("    ediInfo: ").append(toIndentedString(ediInfo)).append("\n");
        sb.append("    transferDesignnatedType: ")
                .append(toIndentedString(transferDesignnatedType))
                .append("\n");
        sb.append("    identification: ").append(toIndentedString(identification)).append("\n");
        sb.append("    transferDetailResponses: ")
                .append(toIndentedString(transferDetailResponses))
                .append("\n");
        sb.append("    unableDetailInfos: ")
                .append(toIndentedString(unableDetailInfos))
                .append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MonsterBoResponse implements Serializable {

    private Long userId;

    // 種別
    private String idType;

    // モンスター名称
    private String monsterName;

    // レベル
    private Long monsterLevel;

    // 現在の経験
    private Long currentExperience;

    // 次のレベルの経験
    private Long nextExperience;

    // 生成日時
    private Date creationDateTime;

    // 月間報酬
    private Long monthlyPower;
}

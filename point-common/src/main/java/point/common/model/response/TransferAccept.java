package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** TransferAccept */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TransferAccept {
    @JsonAlias("acceptNo")
    private String acceptNo;

    @JsonAlias("acceptDatetime")
    private String acceptDatetime;

    public TransferAccept acceptNo(String acceptNo) {
        this.acceptNo = acceptNo;
        return this;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TransferAccept {\n");

        sb.append("    acceptNo: ").append(toIndentedString(acceptNo)).append("\n");
        sb.append("    acceptDatetime: ").append(toIndentedString(acceptDatetime)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

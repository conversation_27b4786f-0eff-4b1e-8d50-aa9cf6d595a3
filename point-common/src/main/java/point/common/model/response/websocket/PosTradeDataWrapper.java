package point.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.OrderChannel;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.constant.TradeAction;
import point.pos.entity.PosTrade;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PosTradeDataWrapper implements Serializable {
    @Serial private static final long serialVersionUID = 1356305469968077375L;

    private Long id;
    private Long symbolId;
    private Long userId;
    private OrderSide orderSide;
    private BigDecimal price;
    private BigDecimal amount;
    private TradeAction tradeAction;
    private Long orderId;
    private BigDecimal fee;
    private OrderType orderType;
    private OrderChannel orderChannel;
    private BigDecimal jpyConversion;
    private Long targetOrderId;
    private Long targetUserId;
    private BigDecimal assetAmount;
    private Date createdAt;
    private Date updatedAt;

    public PosTradeDataWrapper(PosTrade trade) {
        this.id = trade.getId();
        this.symbolId = trade.getSymbolId();
        this.userId = trade.getUserId();
        this.orderSide = trade.getOrderSide();
        this.price = trade.getPrice();
        this.amount = trade.getAmount();
        this.tradeAction = trade.getTradeAction();
        this.orderId = trade.getOrderId();
        this.fee = trade.getFee();
        this.orderType = trade.getOrderType();
        this.orderChannel = trade.getOrderChannel();
        this.jpyConversion = trade.getJpyConversion();
        this.assetAmount = trade.getAssetAmount();
        this.createdAt = trade.getCreatedAt();
        this.updatedAt = trade.getUpdatedAt();
    }

    public PosTrade unwrap() {
        PosTrade trade = new PosTrade() {};
        trade.setId(id);
        trade.setSymbolId(symbolId);
        trade.setUserId(userId);
        trade.setOrderSide(orderSide);
        trade.setPrice(price);
        trade.setAmount(amount);
        trade.setTradeAction(tradeAction);
        trade.setOrderId(orderId);
        trade.setFee(fee);
        trade.setOrderType(orderType);
        trade.setOrderChannel(orderChannel);
        trade.setJpyConversion(jpyConversion);
        trade.setAssetAmount(assetAmount);
        trade.setCreatedAt(createdAt);
        trade.setUpdatedAt(updatedAt);
        return trade;
    }
}

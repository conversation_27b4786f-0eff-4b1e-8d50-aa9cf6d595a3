package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MailData implements Serializable {

    private static final long serialVersionUID = 332350155679199387L;

    @Getter @Setter private Long id;

    @Getter @Setter private String title;

    @Getter @Setter private String contents;

    @Getter @Setter private String header;

    @Getter @Setter private String footer;
}

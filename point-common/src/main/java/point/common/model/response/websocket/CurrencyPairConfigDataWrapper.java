package point.common.model.response.websocket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.entity.CurrencyPairConfig;
import point.common.model.response.CurrencyPairData;
import point.common.model.response.PosCurrencyPair;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrencyPairConfigDataWrapper implements Serializable {
    @Serial private static final long serialVersionUID = 7855933844751803274L;

    private CurrencyPairConfig config;

    public PosCurrencyPair unwrapPos() {
        return PosCurrencyPair.builder()
                .symbolId(config.getId())
                .tradeType(config.getTradeType().getName())
                .currencyPair(config.getCurrencyPair().getName())
                .minOrderAmount(config.getMinOrderAmount())
                .maxOrderAmount(config.getMaxOrderAmount())
                .posSlippagepercent(config.getPosSlippagePercent())
                .posSpreadPercent(config.getPosSpreadPercent())
                .quotePrecision(config.getCurrencyPair().getQuotePrecision())
                .basePrecision(config.getCurrencyPair().getBasePrecision())
                .precision(config.getCurrencyPair().getAssetPrecision())
                .enabled(config.isEnabled())
                .build();
    }

    public CurrencyPairData unwrap() {
        return new CurrencyPairData(config, "", "");
    }
}

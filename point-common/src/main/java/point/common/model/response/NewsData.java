package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.NewsType;
import point.common.entity.News;

@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewsData implements Serializable {

    private static final long serialVersionUID = 332350155679199387L;

    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NewsElement implements Serializable {
        private static final long serialVersionUID = -6724221163430559710L;

        @Getter @Setter private Long id;

        @Getter @Setter private NewsType newsType;

        @Getter @Setter private String title;

        @Getter @Setter private String contents;

        @Getter @Setter private String link;

        @Getter @Setter private Date date;
    }

    @Getter @Setter private List<NewsElement> newsList = new ArrayList<>();

    public void newsList(List<News> newsList) {
        newsList.forEach(
                news ->
                        this.newsList.add(
                                new NewsElement(
                                        news.getId(),
                                        news.getNewsType(),
                                        news.getTitle(),
                                        news.getContents(),
                                        news.getLink(),
                                        news.getDate())));
    }
}

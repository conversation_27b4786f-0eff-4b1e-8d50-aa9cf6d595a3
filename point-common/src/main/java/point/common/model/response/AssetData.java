package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.serializer.BigDecimalSerializer;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetData implements Serializable {

    private static final long serialVersionUID = -5680701498263865050L;

    private Long userId;

    private Currency currency;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal onhandAmount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal lockedAmount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal unlockedAmount;

    private User user;

    private PointUser pointUser;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal jpyOnhandAmount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal jpyLockedAmount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmt;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmtRate;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal avgAcqUnitPrice;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nowPrice;

    private BigDecimal operatePoints;

    @JsonIgnore private BigDecimal assetTotal;
}

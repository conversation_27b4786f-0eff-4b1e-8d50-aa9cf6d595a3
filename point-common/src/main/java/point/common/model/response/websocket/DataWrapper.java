package point.common.model.response.websocket;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import point.common.util.DigestUtils;

@RequiredArgsConstructor
public abstract class DataWrapper {
    String calculateChecksum(Object data) {
        try {
            return DigestUtils.md5(new ObjectMapper().writeValueAsString(data));
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    public abstract String getChecksum();

    public abstract String getCacheKey();
}

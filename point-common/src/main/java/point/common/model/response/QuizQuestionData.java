package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.entity.QuizQuestionPublishedRecord;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class QuizQuestionData implements Serializable {

    private static final long serialVersionUID = -5680701498263865050L;

    private Long id;

    private String quizNum;

    private String title;

    private String optionA;

    private String optionB;

    private String optionC;

    private String optionD;

    private String userAnswer;

    private String correctAnswer;

    private String correctAnswerContent;

    private String optionAWrongReason;
    private String optionBWrongReason;
    private String optionCWrongReason;
    private String optionDWrongReason;

    public QuizQuestionData(
            QuizQuestionPublishedRecord quizQuestionPublishedRecord, String userAnswer) {
        this.id = quizQuestionPublishedRecord.getQuizQuestion().getId();
        this.quizNum = quizQuestionPublishedRecord.getQuizQuestion().getQuizNum();
        this.title = quizQuestionPublishedRecord.getQuizQuestion().getTitle();
        this.optionA = quizQuestionPublishedRecord.getQuizQuestion().getOptionA();
        this.optionB = quizQuestionPublishedRecord.getQuizQuestion().getOptionB();
        this.optionC = quizQuestionPublishedRecord.getQuizQuestion().getOptionC();
        this.optionD = quizQuestionPublishedRecord.getQuizQuestion().getOptionD();
        this.correctAnswer = quizQuestionPublishedRecord.getQuizQuestion().getAnswer();
        this.userAnswer = userAnswer;
        this.correctAnswerContent =
                quizQuestionPublishedRecord.getQuizQuestion().getCorrectAnswerContent();
        this.optionAWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionAWrongReason();
        this.optionBWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionBWrongReason();
        this.optionCWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionCWrongReason();
        this.optionDWrongReason =
                quizQuestionPublishedRecord.getQuizQuestion().getOptionDWrongReason();
    }

    public QuizQuestionData(QuizQuestionPublishedRecord quizQuestionPublishedRecord) {
        this.id = quizQuestionPublishedRecord.getQuizQuestion().getId();
        this.quizNum = quizQuestionPublishedRecord.getQuizQuestion().getQuizNum();
        this.title = quizQuestionPublishedRecord.getQuizQuestion().getTitle();
        this.optionA = quizQuestionPublishedRecord.getQuizQuestion().getOptionA();
        this.optionB = quizQuestionPublishedRecord.getQuizQuestion().getOptionB();
        this.optionC = quizQuestionPublishedRecord.getQuizQuestion().getOptionC();
        this.optionD = quizQuestionPublishedRecord.getQuizQuestion().getOptionD();
    }
}

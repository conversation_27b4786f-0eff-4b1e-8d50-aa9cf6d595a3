package point.common.model.request;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChoiceRewardSearchForm extends PagableSearchForm {

    // ユーザーID(投票)
    private Long userId;

    // ユーザーID(変更)
    private Long userIdChange;

    // 獲得ポイント数From
    private BigDecimal rewardPointsFrom;

    // 獲得ポイント数To
    private BigDecimal rewardPointsTo;

    // 報酬分配日時From
    private Long rewardDateFrom;

    // 報酬分配日時To
    private Long rewardDateTo;

    // 獲得ポイント残高From
    private BigDecimal rewardBalanceFrom;

    // 獲得ポイント残高To
    private BigDecimal rewardBalanceTo;

    // 引出日時From
    private Long withdrawalDateFrom;

    // 引出日時To
    private Long withdrawalDateTo;
}

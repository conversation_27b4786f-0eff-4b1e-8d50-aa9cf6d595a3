package point.common.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class CropGrowthStageUpdateForm extends IdForm {
    @Getter @Setter private Boolean creatFlag;

    @Getter @Setter @NotNull private Long growthStageId;

    @Getter @Setter private String growthStage;

    @Getter @Setter @NotNull private Long evaluationFrom;

    @Getter @Setter @NotNull private Long evaluationTo;
}

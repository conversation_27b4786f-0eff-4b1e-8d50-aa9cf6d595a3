package point.common.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChoiceVoteModifyForm {
    @NotNull private Long choiceVoteId;
    @NotNull private Long activityId;
    @NotNull private Long votePower;
}
